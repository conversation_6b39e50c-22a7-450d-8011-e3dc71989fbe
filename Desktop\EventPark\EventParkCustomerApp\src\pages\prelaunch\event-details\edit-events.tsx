import { useNavigate } from 'react-router-dom';
import { useState, useRef, useEffect, useCallback } from 'react';
import { RefreshCw, X } from 'lucide-react';
import { ArrowLeft, Calendar, Clock, CloseCircle, Image } from 'iconsax-react';
import { formatDate, formattingTime } from '../../../lib/helpers';
import { useEventStore } from '../../../lib/store/event';
import { format } from 'date-fns';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  events,
  UpdateEventPayload,
  EventCategories,
} from '../../../lib/services/events';
import { useEventManagement } from '../../../lib/hooks/useEventManagement';
import { toast } from 'react-toastify';
import {
  EditEventModal,
  SwitchEventPreferenceModal,
  EventSavedSuccessModal,
  ImageUploadModal,
} from '../../../components/modals';
import { EventDatePicker } from '../../../components/date-picker';
import { EditEventFormData } from '../../../types/editEvent';
import { DateRange } from 'react-day-picker';
import { useCompleteEventData } from '../../../lib/hooks/useCompleteEventData';
import { AuthServices } from '../../../lib/services/auth';
import open from '../../../assets/images/open.png';
import priv from '../../../assets/images/private.png';

interface ImageWithUploadState {
  id: string;
  file: File;
  preview: string;
  isUploading?: boolean;
  uploadSuccess?: boolean;
  uploadError?: string | null;
}

export const EditEvents = () => {
  const navigate = useNavigate();
  const { selectedEvent } = useEventStore();
  const { updateEventOptimistically } = useEventManagement();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isSwitchModalOpen, setIsSwitchModalOpen] = useState(false);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const [isImageUploadModalOpen, setIsImageUploadModalOpen] = useState(false);
  const [pendingPreference, setPendingPreference] = useState<string>('');
  const [formData, setFormData] = useState<EditEventFormData>({
    eventName: '',
    eventDescription: '',
    eventDate: '',
    eventTime: '',
    preference: 'Private Event',
    location: '',
    locationPlaceId: '',
    categoryId: '',
    categoryName: '',
  });
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(
    selectedEvent?.date_from
      ? {
          from: new Date(selectedEvent.date_from),
          to: selectedEvent?.date_to
            ? new Date(selectedEvent.date_to)
            : undefined,
        }
      : undefined
  );
  const [showTimePicker, setShowTimePicker] = useState(false);
  const timePickerRef = useRef<HTMLDivElement>(null);
  const [changingImageId, setChangingImageId] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const uploadFileInputRef = useRef<HTMLInputElement>(null);
  const fileDialogOpenedRef = useRef<boolean>(false);
  const [uploadingImages, setUploadingImages] = useState<
    ImageWithUploadState[]
  >([]);

  const {
    completeEventData,
    isLoading: isLoadingCompleteData,
    error: completeDataError,
    refetch: refetchCompleteEventData,
  } = useCompleteEventData(selectedEvent?.id);

  const { data: categoriesData } = useQuery({
    queryKey: ['eventCategories'],
    queryFn: () => events.getEventCategories(),
  });

  const categories: EventCategories[] = categoriesData?.data || [];
  // console.log('complete events', completeEventData);
  const images = completeEventData?.images || [];

  const [hoveredImage, setHoveredImage] = useState<string | null>(null);
  const timeOptions = Array.from({ length: 48 }, (_, i) => {
    const hour = Math.floor(i / 2);
    const minute = i % 2 === 0 ? '00' : '30';
    return `${hour.toString().padStart(2, '0')}:${minute}`;
  });
  const updateEventMutation = useMutation({
    mutationFn: (payload: UpdateEventPayload) => {
      if (!selectedEvent?.id) throw new Error('No event selected');
      return events.updateEventDetails(selectedEvent.id, payload);
    },
    onSuccess: (response) => {
      const updatedEvent = response.data;
      updateEventOptimistically(updatedEvent);
      refetchCompleteEventData(true); // Silent refetch
      handleSaveSuccess();
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to update event';
      toast.error(errorMessage);
    },
  });

  const deleteImageMutation = useMutation({
    mutationFn: (imageId: string) => {
      return events.deleteEventImage(imageId);
    },
    onSuccess: () => {
      toast.success('Image deleted successfully!');
      refetchCompleteEventData(true); // Silent refetch
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to delete image';
      toast.error(errorMessage);
    },
  });

  const deleteImageForChangeMutation = useMutation({
    mutationFn: (imageId: string) => {
      console.log('Deleting image for change:', imageId);
      return events.deleteEventImage(imageId);
    },
    onSuccess: () => {
      console.log('Image deletion for change successful');
      // Don't show toast or refetch here - only after upload completes
    },
    onError: (error: unknown) => {
      console.error('Image deletion for change failed:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to delete image';
      toast.error(errorMessage);
    },
  });

  const uploadImageMutation = useMutation({
    mutationFn: ({ file, eventId }: { file: File; eventId: string }) => {
      console.log('Uploading new image for event:', eventId);
      return AuthServices.uploadFiles(file, 'event_image', eventId);
    },
    onSuccess: () => {
      console.log('Image upload successful');
      toast.success('Image changed successfully!');
      setChangingImageId(null);
      refetchCompleteEventData(true); // Only refetch once after upload
    },
    onError: (error: unknown) => {
      console.error('Image upload failed:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to upload new image';
      toast.error(errorMessage);
      setChangingImageId(null);
    },
  });

  const uploadNewImagesMutation = useMutation({
    mutationFn: async ({
      imageData,
      eventId,
    }: {
      imageData: ImageWithUploadState;
      eventId: string;
    }) => {
      console.log('Uploading single image for event:', eventId);
      return AuthServices.uploadFiles(imageData.file, 'event_image', eventId);
    },
    onSuccess: (_, variables) => {
      console.log('Single image upload successful');
      const { imageData } = variables;

      // Remove the successfully uploaded image from uploading state immediately
      setUploadingImages((prev) => {
        const updatedImages = prev.filter((img) => img.id !== imageData.id);

        // Clean up the object URL for the successfully uploaded image
        URL.revokeObjectURL(imageData.preview);

        return updatedImages;
      });

      // Show individual success toast and refetch to get the new image in main section
      // toast.success('Image uploaded successfully!');
      refetchCompleteEventData(true);
    },
    onError: (error: unknown, variables) => {
      console.error('Single image upload failed:', error);
      const { imageData } = variables;
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to upload image';

      // Update the specific image's error state
      setUploadingImages((prev) =>
        prev.map((img) =>
          img.id === imageData.id
            ? { ...img, isUploading: false, uploadError: errorMessage }
            : img
        )
      );
    },
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleDateRangeSelect = (range: DateRange | undefined) => {
    if (range) {
      setDateRange(range);
      if (range.from) {
        const dateStr = format(range.from, 'yyyy-MM-dd');
        handleInputChange('eventDate', dateStr);
      }
    }
  };

  const handleTimeSelect = (time: string) => {
    handleInputChange('eventTime', time);
    setShowTimePicker(false);
  };

  const handlePreferenceChangeRequest = (newPreference: string) => {
    setPendingPreference(newPreference);
    setIsSwitchModalOpen(true);
  };

  const handleConfirmPreferenceSwitch = () => {
    handleInputChange('preference', pendingPreference);
    setIsSwitchModalOpen(false);
    setPendingPreference('');
  };

  const handleCancelPreferenceSwitch = () => {
    setIsSwitchModalOpen(false);
    setPendingPreference('');
  };
  const handleSaveEvent = () => {
    if (!selectedEvent?.id) {
      toast.error('No event selected');
      return;
    }

    const preferenceToVisibility = (preference: string) => {
      switch (preference) {
        case 'Open Event':
          return 'public';
        case 'Private Event':
          return 'private';
        case 'Invite Only':
          return 'invite_only';
        default:
          return 'private';
      }
    };

    const convertTo24HourFormat = (time12h: string) => {
      const time12hLower = time12h.toLowerCase().trim();

      if (!time12hLower.includes('am') && !time12hLower.includes('pm')) {
        return time12h;
      }

      const [time, modifier] = time12hLower.split(/\s*(am|pm)\s*/);
      const timeParts = time.split(':').map(Number);
      let hours = timeParts[0];
      const minutes = timeParts[1];

      if (isNaN(hours) || isNaN(minutes)) {
        throw new Error('Invalid time format');
      }

      if (modifier === 'pm' && hours !== 12) {
        hours += 12;
      } else if (modifier === 'am' && hours === 12) {
        hours = 0;
      }

      return `${hours.toString().padStart(2, '0')}:${minutes
        .toString()
        .padStart(2, '0')}`;
    };

    const formatDateTimeForAPI = (date: string, time: string) => {
      if (!date || !time) {
        throw new Error('Date and time are required');
      }

      const time24h = convertTo24HourFormat(time);

      const timeParts = time24h.split(':');
      if (timeParts.length !== 2) {
        throw new Error('Invalid time format. Expected HH:MM');
      }

      const [hours, minutes] = timeParts.map(Number);

      if (
        isNaN(hours) ||
        isNaN(minutes) ||
        hours < 0 ||
        hours > 23 ||
        minutes < 0 ||
        minutes > 59
      ) {
        throw new Error('Invalid time values');
      }

      const eventDate = new Date(date);

      if (isNaN(eventDate.getTime())) {
        throw new Error('Invalid date format');
      }

      eventDate.setHours(hours, minutes, 0, 0);

      if (isNaN(eventDate.getTime())) {
        throw new Error('Invalid date/time combination');
      }

      return eventDate.toISOString();
    };

    const payload: UpdateEventPayload = {
      title: formData.eventName,
      description: formData.eventDescription,
      visibility: preferenceToVisibility(formData.preference),
    };

    if (formData.eventDate && formData.eventTime) {
      try {
        payload.date_from = formatDateTimeForAPI(
          formData.eventDate,
          formData.eventTime
        );

        if (dateRange?.to) {
          payload.date_to = formatDateTimeForAPI(
            format(dateRange.to, 'yyyy-MM-dd'),
            formData.eventTime
          );
        } else {
          payload.date_to = payload.date_from;
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Invalid date/time format';
        toast.error(`Date/Time Error: ${errorMessage}`);
        return;
      }
    }

    if (formData.categoryId) {
      payload.category_id = formData.categoryId;
    }

    if (formData.locationPlaceId) {
      payload.location_place_id = formData.locationPlaceId;
    }

    updateEventMutation.mutate(payload);
  };

  const handleSaveSuccess = () => {
    setIsEditModalOpen(false);
    setIsSuccessModalOpen(true);
  };

  const handleDeleteImage = (imageId: string) => {
    deleteImageMutation.mutate(imageId);
  };

  const validateImageFile = (file: File): boolean => {
    const MAX_FILE_SIZE_MB = Number(import.meta.env.VITE_MAX_IMAGE_SIZE) || 10;
    const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

    if (!file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return false;
    }

    if (file.size > MAX_FILE_SIZE_BYTES) {
      toast.error(`Image size must be less than ${MAX_FILE_SIZE_MB}MB`);
      return false;
    }

    return true;
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    console.log('File change event triggered');
    const file = event.target.files?.[0];

    // Clear the file dialog opened flag immediately
    fileDialogOpenedRef.current = false;

    // If no file is selected (user cancelled), reset the changing state
    if (!file) {
      console.log('No file selected, resetting changingImageId');
      setChangingImageId(null);
      return;
    }

    console.log('File selected:', file.name, 'for image:', changingImageId);

    if (!changingImageId || !selectedEvent?.id) {
      console.log('Missing changingImageId or selectedEvent.id, resetting');
      setChangingImageId(null);
      return;
    }

    if (!validateImageFile(file)) {
      console.log('File validation failed');
      setChangingImageId(null);
      return;
    }

    try {
      console.log('Starting image change process...');
      // First delete the old image (using the silent mutation)
      await deleteImageForChangeMutation.mutateAsync(changingImageId);

      // Then upload the new image
      await uploadImageMutation.mutateAsync({
        file,
        eventId: selectedEvent.id,
      });
      console.log('Image change process completed successfully');
    } catch (error) {
      console.error('Error changing image:', error);
      // Error handling is done in the mutations
      setChangingImageId(null);
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleFileInputClick = () => {
    fileDialogOpenedRef.current = true;
  };

  const handleWindowFocus = useCallback(() => {
    // When window regains focus after file dialog, check if file was selected
    if (fileDialogOpenedRef.current) {
      setTimeout(() => {
        // Only reset if the file dialog was opened but no file change event occurred
        if (
          fileInputRef.current &&
          !fileInputRef.current.files?.length &&
          changingImageId &&
          fileDialogOpenedRef.current // Double check flag hasn't been cleared
        ) {
          setChangingImageId(null);
        }
        fileDialogOpenedRef.current = false;
      }, 200); // Increased timeout to give more time for file change event
    }
  }, [changingImageId]);

  const handleChangeImage = (imageId: string) => {
    console.log('Change image clicked for:', imageId);

    // Reset any previous file selection
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    setChangingImageId(imageId);
    console.log('Set changingImageId to:', imageId);

    // Small delay to ensure state is set before triggering file dialog
    setTimeout(() => {
      console.log('Triggering file input click');
      fileInputRef.current?.click();
    }, 10);
  };

  const handleUploadImages = () => {
    console.log('Upload images clicked');
    setIsImageUploadModalOpen(true);
  };

  const uploadSingleImage = async (imageData: ImageWithUploadState) => {
    if (!selectedEvent?.id) {
      return;
    }

    // Mark this image as uploading
    setUploadingImages((prev) =>
      prev.map((img) =>
        img.id === imageData.id
          ? { ...img, isUploading: true, uploadError: null }
          : img
      )
    );

    try {
      await uploadNewImagesMutation.mutateAsync({
        imageData,
        eventId: selectedEvent.id,
      });
    } catch (error) {
      console.error(`Upload failed for ${imageData.file.name}:`, error);
      // Error handling is done in the mutation
    }
  };

  const retryImageUpload = (imageId: string) => {
    const imageToRetry = uploadingImages.find((img) => img.id === imageId);
    if (imageToRetry) {
      uploadSingleImage(imageToRetry);
    }
  };

  const removeUploadingImage = (id: string) => {
    setUploadingImages((prev) => {
      const imageToRemove = prev.find((img) => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.preview);
      }
      return prev.filter((img) => img.id !== id);
    });
  };

  const handleNewImagesFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    console.log('New images file change event triggered');
    const files = Array.from(event.target.files || []);

    if (files.length === 0) {
      console.log('No files selected');
      return;
    }

    if (!selectedEvent?.id) {
      console.log('Missing selectedEvent.id');
      toast.error('No event selected');
      return;
    }

    // Check total image limit (existing + uploading + new)
    const totalImages = images.length + uploadingImages.length + files.length;
    if (totalImages > 5) {
      const allowedCount = 5 - images.length - uploadingImages.length;
      if (allowedCount <= 0) {
        toast.error(
          'Maximum of 5 images allowed. Please delete some existing images first.'
        );
        return;
      }
      toast.error(
        `Maximum of 5 images allowed. Only ${allowedCount} image(s) can be uploaded.`
      );
      files.splice(allowedCount); // Keep only allowed files
    }

    console.log(
      'Files selected:',
      files.map((f) => f.name)
    );

    // Create image objects with upload state
    const newImages: ImageWithUploadState[] = [];
    const rejectedFiles: string[] = [];

    files.forEach((file) => {
      if (!validateImageFile(file)) {
        rejectedFiles.push(file.name);
        return;
      }

      const id = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      const preview = URL.createObjectURL(file);
      newImages.push({
        id,
        file,
        preview,
        isUploading: false,
        uploadSuccess: false,
        uploadError: null,
      });
    });

    if (rejectedFiles.length > 0) {
      toast.warning(
        `Some files were skipped due to validation errors: ${rejectedFiles.join(
          ', '
        )}`
      );
    }

    if (newImages.length === 0) {
      console.log('No valid files after validation');
      return;
    }

    // Add images to uploading state
    setUploadingImages((prev) => [...prev, ...newImages]);

    // Upload images one by one
    for (const imageData of newImages) {
      await uploadSingleImage(imageData);
      // Add a small delay between uploads to avoid overwhelming the server
      await new Promise((resolve) => setTimeout(resolve, 500));
    }

    // Reset file input
    if (uploadFileInputRef.current) {
      uploadFileInputRef.current.value = '';
    }
  };

  useEffect(() => {
    if (completeEventData) {
      const visibilityToPreference = (visibility: string) => {
        switch (visibility?.toLowerCase()) {
          case 'public':
            return 'Open Event';
          case 'private':
            return 'Private Event';
          case 'invite_only':
            return 'Invite Only';
          default:
            return 'Private Event';
        }
      };

      setFormData({
        eventName: completeEventData.title || '',
        eventDescription: completeEventData.description || '',
        eventDate: completeEventData.date_from
          ? format(new Date(completeEventData.date_from), 'yyyy-MM-dd')
          : '',
        eventTime: completeEventData.date_from
          ? formattingTime(completeEventData.date_from)
          : '',
        preference: visibilityToPreference(completeEventData.visibility),
        location: completeEventData.location_address || '',
        locationPlaceId: '',
        categoryId: completeEventData.category_id || '',
        categoryName: completeEventData.category_name || '',
      });

      if (completeEventData.date_from) {
        const fromDate = new Date(completeEventData.date_from);
        const toDate = completeEventData.date_to
          ? new Date(completeEventData.date_to)
          : undefined;
        setDateRange({
          from: fromDate,
          to: toDate,
        });
      }
    }
  }, [completeEventData]);

  // Cleanup URL objects on unmount
  useEffect(() => {
    const currentUploadingImages = uploadingImages;
    return () => {
      currentUploadingImages.forEach((image) => {
        URL.revokeObjectURL(image.preview);
      });
    };
  }, [uploadingImages]);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        timePickerRef.current &&
        !timePickerRef.current.contains(event.target as Node)
      ) {
        setShowTimePicker(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (showDatePicker) {
          setShowDatePicker(false);
        } else if (showTimePicker) {
          setShowTimePicker(false);
        } else if (isImageUploadModalOpen) {
          setIsImageUploadModalOpen(false);
        } else if (isEditModalOpen) {
          setIsEditModalOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
    window.addEventListener('focus', handleWindowFocus);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, [
    showDatePicker,
    showTimePicker,
    isEditModalOpen,
    isImageUploadModalOpen,
    changingImageId,
    handleWindowFocus,
  ]);

  const eventDate = formatDate(
    completeEventData?.date_from || selectedEvent?.date_from
  );
  const eventTime = formattingTime(
    completeEventData?.date_from || selectedEvent?.date_from
  );

  const dateRangeText =
    dateRange?.from && dateRange?.to
      ? `${format(dateRange.from, 'MMM dd')} - ${format(
          dateRange.to,
          'MMM dd, yyyy'
        )}`
      : dateRange?.from
      ? format(dateRange.from, 'MMM dd, yyyy')
      : 'Select Date Range';

  if (completeDataError)
    return (
      <div className="flex justify-center items-center min-h-[100vh]">
        Sorry, An Error Occured, Kindly Refresh
      </div>
    );
  if (isLoadingCompleteData)
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
      </div>
    );
  return (
    <div className="bg-[linear-gradient(229.47deg,_#FEFAF8_38.81%,_#F5F6FE_851.11%)] min-h-screen pb-7">
      <div className=" max-w-[1160px] mx-auto px-4 sm:px-6 lg:px-0">
        <div className="flex items-center gap-3 py-4 sm:py-6 lg:py-8">
          <button
            type="button"
            onClick={() => navigate(-1)}
            className={` bg-white rounded-full cursor-pointer `}>
            <div className="bg-[#F5F6FE] w-fit p-0.5">
              <div className="bg-primary p-0.5 rounded-full">
                <ArrowLeft size="16" color="#fff" />
              </div>{' '}
            </div>
          </button>
          <h1 className="text-base sm:text-lg font-semibold text-gray-900">
            Event Details
          </h1>
        </div>
        <div className="">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mb-6 sm:mb-8 bg-white py-4 sm:py-6 lg:py-9 px-3 sm:px-6 lg:px-10 rounded-lg sm:rounded-none">
            <div className="w-full sm:w-[130px] h-[200px] sm:h-[130px] rounded-xl overflow-hidden flex-shrink-0">
              <img
                src={
                  completeEventData?.banner_preview_url ||
                  'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=800&h=600&fit=crop'
                }
                alt="Event Banner"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex flex-col sm:flex-row sm:items-center  gap-3 sm:gap-4">
                <h2 className="text-xl sm:text-2xl lg:text-[36px] font-medium leading-tight break-words pr-2 sm:pr-0">
                  {completeEventData?.title || ''}
                </h2>
                <div className="flex items-center gap-2">
                  {completeEventData?.visibility === 'public' ? (
                    <>
                      <img src={open} alt="open-events" className="w-8 h-8" />
                      <span className="text-gray-500 font-medium text-sm tracking-wider">
                        OPEN EVENT
                      </span>
                    </>
                  ) : (
                    <>
                      <img
                        src={priv}
                        alt="private-events"
                        className="w-8 h-8"
                      />
                      <span className="text-gray-500 font-medium text-sm tracking-wider">
                        PRIVATE EVENT
                      </span>
                    </>
                  )}
                </div>
              </div>
              <p className="text-grey-250 text-sm sm:text-base mt-2 break-words pr-2 sm:pr-0">
                {completeEventData?.description || ''}{' '}
              </p>
              <div className="flex flex-wrap gap-2 sm:gap-4 my-3 sm:my-4">
                <div className="flex items-center gap-1 bg-cus-pink-500 pl-2.5 pr-2 py-0.5 rounded-2xl">
                  <Calendar color="#FF885E" size={12} variant="Bulk" />{' '}
                  <span className="text-xs italic font-medium text-cus-orange-250">
                    {eventDate}
                  </span>
                </div>
                <div className="flex items-center gap-1 bg-primary-700 pl-2.5 pr-2 rounded-2xl py-0.5">
                  <Clock color="#000073" size={12} variant="Bulk" />{' '}
                  <span className="text-dark-blue text-xs italic font-medium">
                    {eventTime}
                  </span>
                </div>
              </div>
              <div className="mt-2 uppercase text-grey-950 text-xs italic break-words pr-2 sm:pr-0">
                📌 <span>{completeEventData?.location_address || ''}</span>
              </div>
            </div>
            <button
              onClick={() => setIsEditModalOpen(true)}
              className="text-dark-blue-100 text-sm bg-primary-250 px-3.5 py-2 rounded-full font-semibold whitespace-nowrap self-start">
              Edit Details
            </button>
          </div>
          {images.length > 0 && (
            <div className="mb-6 px-3 sm:px-0">
              <h3 className="text-base xs:text-lg font-semibold mb-3 xs:mb-4 text-gray-900">
                Event Images
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 xs:gap-4">
                {images.map((image, index) => (
                  <div
                    key={image.id}
                    className="relative group cursor-pointer rounded-xl overflow-hidden"
                    onMouseEnter={() => setHoveredImage(image.id)}
                    onMouseLeave={() => setHoveredImage(null)}>
                    <img
                      src={image.preview_url}
                      alt={`Event image ${index + 1}`}
                      className="w-full h-[300px] xs:h-[350px] sm:h-[400px] md:h-[500px] object-cover"
                    />
                    {hoveredImage === image.id && (
                      <div className="absolute inset-0 bg-black/50 flex items-center justify-center space-x-2 xs:space-x-3 p-2">
                        <button
                          onClick={() => handleChangeImage(image.id)}
                          className="px-2 xs:px-3 md:px-4 py-1.5 xs:py-2 bg-white text-gray-700 rounded-lg text-xs xs:text-sm font-medium hover:bg-gray-100 flex items-center space-x-1 xs:space-x-2"
                          disabled={
                            changingImageId === image.id ||
                            deleteImageMutation.isPending ||
                            deleteImageForChangeMutation.isPending ||
                            uploadImageMutation.isPending
                          }>
                          <RefreshCw
                            size={12}
                            className={`xs:w-3.5 xs:h-3.5 ${
                              changingImageId === image.id ? 'animate-spin' : ''
                            }`}
                          />
                          <span className="hidden xs:inline">
                            {changingImageId === image.id
                              ? 'Changing...'
                              : 'Change Image'}
                          </span>
                          <span className="xs:hidden">
                            {changingImageId === image.id
                              ? 'Changing...'
                              : 'Change'}
                          </span>
                        </button>
                        <button
                          onClick={() => handleDeleteImage(image.id)}
                          className="px-2 xs:px-3 md:px-4 py-1.5 xs:py-2 bg-red-600 text-white rounded-lg text-xs xs:text-sm font-medium hover:bg-red-700 flex items-center space-x-1 xs:space-x-2"
                          disabled={
                            deleteImageMutation.isPending ||
                            deleteImageForChangeMutation.isPending ||
                            changingImageId === image.id ||
                            uploadImageMutation.isPending
                          }>
                          <X size={12} className="xs:w-3.5 xs:h-3.5" />
                          <span className="hidden xs:inline">
                            {deleteImageMutation.isPending
                              ? 'Deleting...'
                              : 'Delete Image'}
                          </span>
                          <span className="xs:hidden">
                            {deleteImageMutation.isPending
                              ? 'Del...'
                              : 'Delete'}
                          </span>
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Add More Images Button */}
              {images.length + uploadingImages.length < 5 && (
                <div className="mt-6 text-center">
                  <button
                    onClick={() => handleUploadImages()}
                    disabled={uploadNewImagesMutation.isPending}
                    className="px-20 py-3 bg-[#4D55F2] text-white  border border-primary rounded-full font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 mx-auto">
                    {uploadNewImagesMutation.isPending ? (
                      <>
                        <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full" />
                        Uploading...
                      </>
                    ) : (
                      <>Upload more Images</>
                    )}
                  </button>
                </div>
              )}

              {/* {images.length + uploadingImages.length >= 5 && (
                <div className="mt-4 text-center">
                  <p className="text-sm text-gray-500">
                    Maximum of 5 images reached. Delete an image to add more.
                  </p>
                </div>
              )} */}
            </div>
          )}

          {/* Uploading Images Section */}
          {uploadingImages.length > 0 && (
            <div className="mb-6 px-3 sm:px-0">
              <h3 className="text-base xs:text-lg font-semibold mb-3 xs:mb-4 text-gray-900">
                {images.length > 0 ? 'Uploading Images' : 'Event Images'}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 xs:gap-4">
                {uploadingImages.map((image, index) => (
                  <div
                    key={image.id}
                    className="relative rounded-xl overflow-hidden">
                    {/* Skeleton loader overlay for uploading state */}
                    {image.isUploading && (
                      <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-xl z-10 flex items-center justify-center">
                        <div className="w-8 h-8 bg-gray-300 rounded-full animate-pulse"></div>
                      </div>
                    )}

                    <img
                      src={image.preview}
                      alt={`Uploading image ${index + 1}`}
                      className={`w-full h-[300px] xs:h-[350px] sm:h-[400px] md:h-[500px] object-cover transition-opacity duration-300 ${
                        image.isUploading
                          ? 'opacity-50'
                          : image.uploadError
                          ? 'opacity-70'
                          : 'opacity-100'
                      }`}
                    />

                    {/* Upload status indicators */}
                    {image.isUploading && (
                      <div className="absolute bottom-2 left-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded text-center z-20">
                        Uploading...
                      </div>
                    )}

                    {image.uploadError && !image.isUploading && (
                      <>
                        <div className="absolute bottom-2 left-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded text-center z-20">
                          Upload Failed
                        </div>
                        <div className="absolute inset-0 flex items-center justify-center z-20">
                          <button
                            onClick={() => retryImageUpload(image.id)}
                            className="bg-red-500 text-white rounded-full w-12 h-12 flex items-center justify-center text-lg hover:bg-red-600 transition-colors shadow-lg"
                            title="Retry upload">
                            ↻
                          </button>
                        </div>
                      </>
                    )}

                    {/* Remove button (visible for successful uploads and errors, hidden during upload) */}
                    {!image.isUploading && (
                      <button
                        onClick={() => removeUploadingImage(image.id)}
                        className="absolute top-2 right-2 bg-white rounded-full z-20">
                        <CloseCircle size="20" color="#CC0000" variant="Bold" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {images.length === 0 &&
            uploadingImages.length === 0 &&
            !isLoadingCompleteData && (
              <div className="mb-6  rounded-xl p-4 xs:p-6 sm:p-8 text-center mx-3 sm:mx-0">
                <div className=" bg-[#B8BBFA]/10 rounded-[32px] mx-auto w-fit mb-10">
                  <Image size="200" variant="Bulk" color="#B8BBFA" />
                </div>
                <h3 className="text-base md:text-[28px] font-medium text-gray-900 mb-2">
                  No Images Uploaded
                </h3>
                <p className="text-sm md:text-base text-gray-500 mb-2">
                  You have not uploaded any images for your event yet,
                </p>
                <p className="text-sm md:text-base text-gray-500 mb-10">
                  Get started by clicking the button below{' '}
                </p>
                <button
                  onClick={() => handleUploadImages()}
                  disabled={uploadNewImagesMutation.isPending}
                  className="px-6 py-2 text-sm bg-primary text-white rounded-full font-semibold hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 mx-auto">
                  {uploadNewImagesMutation.isPending ? (
                    <>
                      <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                      Uploading...
                    </>
                  ) : (
                    'Upload Images'
                  )}
                </button>
              </div>
            )}

          {isLoadingCompleteData && (
            <div className="mb-6 bg-white rounded-xl p-4 xs:p-6 sm:p-8 text-center mx-3 sm:mx-0">
              <div className="animate-spin h-6 w-6 xs:h-8 xs:w-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-3 xs:mb-4" />
              <p className="text-sm xs:text-base text-gray-500">
                Loading images...
              </p>
            </div>
          )}
        </div>
      </div>

      <EditEventModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        formData={formData}
        onInputChange={handleInputChange}
        isLoading={updateEventMutation.isPending}
        dateRangeText={dateRangeText}
        onDatePickerOpen={() => setShowDatePicker(true)}
        showTimePicker={showTimePicker}
        onTimePickerToggle={() => setShowTimePicker(true)}
        timeOptions={timeOptions}
        onTimeSelect={handleTimeSelect}
        timePickerRef={timePickerRef}
        onPreferenceChangeRequest={handlePreferenceChangeRequest}
        onSave={handleSaveEvent}
        onSaveSuccess={handleSaveSuccess}
        categories={categories}
      />

      <SwitchEventPreferenceModal
        isOpen={isSwitchModalOpen}
        onClose={handleCancelPreferenceSwitch}
        onConfirm={handleConfirmPreferenceSwitch}
        currentPreference={formData.preference}
        targetPreference={pendingPreference}
      />

      <EventSavedSuccessModal
        isOpen={isSuccessModalOpen}
        onClose={() => setIsSuccessModalOpen(false)}
      />

      <EventDatePicker
        isOpen={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        dateRange={dateRange}
        onDateRangeSelect={handleDateRangeSelect}
      />

      <ImageUploadModal
        isOpen={isImageUploadModalOpen}
        onClose={() => setIsImageUploadModalOpen(false)}
        onUploadComplete={() => {
          refetchCompleteEventData(true);
        }}
        existingImages={images.map((img) => ({
          id: img.id,
          preview_url: img.preview_url,
        }))}
        onDeleteImage={handleDeleteImage}
      />

      {/* Hidden file input for image change */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        onClick={handleFileInputClick}
        className="hidden"
      />

      {/* Hidden file input for uploading new images */}
      <input
        ref={uploadFileInputRef}
        type="file"
        accept="image/*"
        multiple
        onChange={handleNewImagesFileChange}
        className="hidden"
      />
    </div>
  );
};
