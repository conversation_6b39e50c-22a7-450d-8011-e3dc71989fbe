[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = https://github.com/EventPark/EventParkCustomerApp.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
	vscode-merge-base = origin/main
[branch "contributors"]
	vscode-merge-base = origin/main
[branch "contributor"]
	vscode-merge-base = origin/main
[branch "nudge-fix"]
	vscode-merge-base = origin/main
[branch "fix-activation"]
	vscode-merge-base = origin/main
[branch "edit-event"]
	vscode-merge-base = origin/main
[branch "tools-check-update"]
	vscode-merge-base = origin/main
[branch "iv-template"]
	vscode-merge-base = origin/main
[branch "iv-upload"]
	vscode-merge-base = origin/main
[branch "tool-update"]
	vscode-merge-base = origin/main
[branch "upload-fix"]
	vscode-merge-base = origin/main
[branch "guest-count"]
	vscode-merge-base = origin/main
[branch "update-transaction-pin"]
	vscode-merge-base = origin/main
[branch "settings"]
	vscode-merge-base = origin/main
[branch "fixes"]
	vscode-merge-base = origin/main
[branch "item-parse"]
	vscode-merge-base = origin/main
[branch "keeping"]
	vscode-merge-base = origin/main
[branch "welcome-page"]
	vscode-merge-base = origin/main
[branch "delivery-address"]
	vscode-merge-base = origin/main
[branch "password-validate"]
	vscode-merge-base = origin/main
[branch "quick-act"]
	vscode-merge-base = origin/main
[branch "confirm-modal"]
	vscode-merge-base = origin/main
[branch "empty-state"]
	vscode-merge-base = origin/main
[branch "calendar-fix"]
	vscode-merge-base = origin/main
[branch "new-branch"]
	vscode-merge-base = origin/main
[branch "delete-event-image"]
	vscode-merge-base = origin/main
[branch "dashboard-dropdown"]
	remote = origin
	merge = refs/heads/dashboard-dropdown
	vscode-merge-base = origin/dashboard-dropdown
