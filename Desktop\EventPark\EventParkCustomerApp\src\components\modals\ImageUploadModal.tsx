import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CloseCircle, ArrowRight2 } from 'iconsax-react';
import { toast } from 'react-toastify';
import { AuthServices } from '../../lib/services/auth';
import { useEventStore } from '../../lib/store/event';
import frame from "../../assets/images/bg-frame.png";

interface ImageWithUploadState {
  id: string;
  file: File;
  preview: string;
  isUploading?: boolean;
  uploadSuccess?: boolean;
  uploadError?: string | null;
}

interface ImageUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUploadComplete?: () => void;
  existingImages?: Array<{
    id: string;
    preview_url: string;
  }>;
  onDeleteImage?: (imageId: string) => void;
}

export const ImageUploadModal: React.FC<ImageUploadModalProps> = ({
  isOpen,
  onClose,
  onUploadComplete,
  existingImages = [],
  onDeleteImage,
}) => {
  const { selectedEvent } = useEventStore();
  const [uploadingImages, setUploadingImages] = useState<
    ImageWithUploadState[]
  >([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const MAX_FILE_SIZE_MB = Number(import.meta.env.VITE_MAX_IMAGE_SIZE) || 10;
  const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
      // Clean up uploadingImages and revoke object URLs when modal closes
      setUploadingImages((prev) => {
        prev.forEach((img) => {
          URL.revokeObjectURL(img.preview);
        });
        return [];
      });
      // Also reset file input if present
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Cleanup URL objects on unmount
  useEffect(() => {
    return () => {
      uploadingImages.forEach((image) => {
        URL.revokeObjectURL(image.preview);
      });
    };
  }, [uploadingImages]);

  const validateImageFile = (file: File): boolean => {
    if (!file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return false;
    }

    if (file.size > MAX_FILE_SIZE_BYTES) {
      toast.error(`File size must be less than ${MAX_FILE_SIZE_MB}MB`);
      return false;
    }

    return true;
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    handleFiles(files);
  };

  const handleFiles = (files: File[]) => {
    if (files.length === 0) return;

    // Check if we already have the maximum number of images
    const totalImages =
      existingImages.length + uploadingImages.length + files.length;
    if (totalImages > 5) {
      const allowedCount = 5 - existingImages.length - uploadingImages.length;
      if (allowedCount <= 0) {
        toast.error(
          'Maximum of 5 images allowed. Please delete some existing images first.'
        );
        return;
      }
      toast.error(
        `Maximum of 5 images allowed. Only ${allowedCount} image(s) can be uploaded.`
      );
      files.splice(allowedCount);
    }

    const newImages: ImageWithUploadState[] = [];
    const rejectedFiles: string[] = [];

    Array.from(files).forEach((file) => {
      if (!validateImageFile(file)) {
        rejectedFiles.push(`${file.name} (invalid format or size)`);
        return;
      }

      const id = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      const preview = URL.createObjectURL(file);
      newImages.push({
        id,
        file,
        preview,
        isUploading: true, // Set to uploading immediately like in add-event-images
        uploadSuccess: false,
        uploadError: null,
      });
    });

    if (rejectedFiles.length > 0) {
      toast.error(`Some files were rejected: ${rejectedFiles.join(', ')}`);
    }

    if (newImages.length > 0) {
      setUploadingImages((prev) => [...prev, ...newImages]);

      // Start uploading each image one by one
      newImages.forEach((imageData) => {
        uploadSingleImage(imageData);
      });
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
  };

  const removeUploadingImage = (id: string) => {
    setUploadingImages((prev) => {
      const imageToRemove = prev.find((img) => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.preview);
      }
      return prev.filter((img) => img.id !== id);
    });
  };

  const uploadSingleImage = async (imageData: ImageWithUploadState) => {
    if (!selectedEvent?.id) {
      toast.error('No event selected');
      return;
    }

    // Mark this image as uploading
    setUploadingImages((prev) =>
      prev.map((img) =>
        img.id === imageData.id
          ? { ...img, isUploading: true, uploadError: null }
          : img
      )
    );

    try {
      await AuthServices.uploadFiles(
        imageData.file,
        'event_image',
        selectedEvent.id
      );

      // Mark the image as successfully uploaded but keep it in the uploading state
      setUploadingImages((prev) =>
        prev.map((img) =>
          img.id === imageData.id
            ? { ...img, isUploading: false, uploadSuccess: true }
            : img
        )
      );

      // toast.success('Image uploaded successfully!');
    } catch (error: unknown) {
      console.error('Image upload failed:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to upload image';

      // Update the specific image's error state
      setUploadingImages((prev) =>
        prev.map((img) =>
          img.id === imageData.id
            ? { ...img, isUploading: false, uploadError: errorMessage }
            : img
        )
      );

      toast.error(errorMessage);
    }
  };

  const retryImageUpload = (imageId: string) => {
    const imageToRetry = uploadingImages.find((img) => img.id === imageId);
    if (imageToRetry) {
      uploadSingleImage(imageToRetry);
    }
  };

  const handleContinue = async () => {
    const hasUploadingImages = uploadingImages.some((img) => img.isUploading);

    if (hasUploadingImages) {
      toast.warning(
        'Please wait for all images to finish uploading before continuing.'
      );
      return;
    }

    const failedImages = uploadingImages.filter((img) => img.uploadError);
    if (failedImages.length > 0) {
      toast.error(
        `${failedImages.length} image(s) failed to upload. Please retry or remove them before continuing.`
      );
      return;
    }

    // Call the upload complete callback to refresh the parent component
    if (onUploadComplete) {
      onUploadComplete();
    }

    // Close modal only when user clicks continue
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-sm"
        onClick={onClose}>
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2 }}
          className="mx-4 w-full max-w-[680px] max-h-[90vh] overflow-y-auto bg-white rounded-2xl shadow-xl"
          onClick={(e) => e.stopPropagation()}>
          <div className="relative p-6">
            {/* Close Button */}
            <button
              onClick={onClose}
              className="absolute right-4 top-4 z-10 rounded-full p-1 hover:bg-gray-100 transition-colors">
              <CloseCircle size={32} variant="Bulk" color="#634C42" />
            </button>

            {/* Existing Images Grid */}
            <div className="mb-6 mt-10 max-w-[507px] mx-auto">
              <div className="grid grid-cols-2 md:grid-cols-4  space-y-8">
                {existingImages.map((image) => (
                  <div key={image.id} className="relative w-[105px]">
                    <img
                      src={image.preview_url}
                      alt="Event image"
                      className="w-full h-32 object-cover rounded-lg"
                    />
                    {onDeleteImage && (
                      <button
                        onClick={() => onDeleteImage(image.id)}
                        className="absolute -top-2 -right-1 bg-white rounded-full shadow-md">
                        <CloseCircle size="20" color="#CC0000" variant="Bold" />
                      </button>
                    )}
                  </div>
                ))}

                {/* Show uploading images in the same grid */}
                {uploadingImages.map((image) => (
                  <div key={image.id} className="relative w-[105px] h-32">
                    {/* Skeleton loader overlay for uploading state */}
                    {image.isUploading && (
                      <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-lg z-10 flex items-center justify-center">
                        <div className="w-6 h-6 bg-gray-300 rounded-full animate-pulse"></div>
                      </div>
                    )}

                    <img
                      src={image.preview}
                      alt="Uploading image"
                      className={`w-full h-32 object-cover rounded-lg transition-opacity duration-300 ${
                        image.isUploading
                          ? 'opacity-50'
                          : image.uploadError
                          ? 'opacity-70'
                          : image.uploadSuccess
                          ? 'opacity-100'
                          : 'opacity-100'
                      }`}
                    />

                    {/* Upload status indicators */}
                    {image.isUploading && (
                      <div className="absolute bottom-1 left-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 py-0.5 rounded text-center z-20">
                        Uploading...
                      </div>
                    )}
                    {image.uploadError && !image.isUploading && (
                      <>
                        <div className="absolute bottom-1 left-1 right-1 bg-red-500 text-white text-xs px-1 py-0.5 rounded text-center z-20">
                          Upload Failed
                        </div>
                        <div className="absolute inset-0 flex items-center justify-center z-20">
                          <button
                            onClick={() => retryImageUpload(image.id)}
                            className="bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm hover:bg-red-600 transition-colors shadow-lg"
                            title="Retry upload">
                            ↻
                          </button>
                        </div>
                      </>
                    )}

                    {/* Remove button */}
                    {!image.isUploading && (
                      <button
                        onClick={() => removeUploadingImage(image.id)}
                        className="absolute -top-2 -right-2 bg-white rounded-full shadow-md z-20">
                        <CloseCircle size="20" color="#CC0000" variant="Bold" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Upload Area - Only show if less than 5 images total */}
            {existingImages.length + uploadingImages.length < 5 && (
             <>
              
         <div className="mb-6 max-w-[507px] mx-auto">
            <div
              className={`rounded-2xl relative overflow-hidden cursor-pointer transition-all ${
                isDragOver ? 'ring-2 ring-primary-650 ring-opacity-50' : ''
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
                  onClick={() => fileInputRef.current?.click()}>
              <div className="w-full h-auto">
                <img
                  src={frame}
                  alt="frame"
                  className="w-full h-auto object-contain"
                />
              </div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="bg-white px-4 py-2 rounded-full ">
                  <p className="text-primary text-sm font-bold italic">
                    Click to Upload Images of your Events
                  </p>
                </div>
              </div>
              
            </div>
            <div className=" flex justify-between items-center mt-2">
              <p className="text-[#6B7280] text-xs">
                Allowed formats: PNG, JPEG, JPG up to {MAX_FILE_SIZE_MB}MB each
              </p>
              <p className="text-[#343CD8] text-xs italic">
                Maximum of 4 selections
              </p>
            </div>
          </div></>
            )}

            {/* Continue Button */}
            <button
              onClick={handleContinue}
              disabled={uploadingImages.some((img) => img.isUploading)}
              className={`w-full mt-20 max-w-[507px] mx-auto text-white py-3 rounded-full font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 ${
                uploadingImages.some((img) => img.isUploading)
                  ? 'bg-primary/60'
                  : uploadingImages.length > 0 &&
                    uploadingImages.every((img) => img.uploadSuccess)
                  ? 'bg-primary hover:bg-primary/90'
                  : 'bg-primary hover:bg-primary/90'
              }`}>
              {uploadingImages.some((img) => img.isUploading) ? (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                  Uploading...
                </>
              ) : (
                <>
                  Continue
                  <ArrowRight2 size="16" color="#fff" />
                </>
              )}
            </button>

            {/* Hidden file input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={handleFileChange}
              className="hidden"
            />
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
