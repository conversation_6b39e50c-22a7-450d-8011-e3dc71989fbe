import { useState } from 'react';
import { Button } from '../../components/button/onboardingButton';
import {
  ArrowCircleRight2,
  AddCircle
} from 'iconsax-react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { events, EventCategories } from '../../lib/services/events';

interface EventCategoryProps {
  onNext: (categoryId: string) => void;
  initialData?: string;
}

export const EventCategory = ({ onNext, initialData }: EventCategoryProps) => {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(
    initialData || null
  );
  const [showCustomInput, setShowCustomInput] = useState(false);
  const [customEventName, setCustomEventName] = useState('');

  const { data: categoriesData, isLoading } = useQuery({
    queryKey: ['eventCategories'],
    queryFn: () => events.getEventCategories(),
  });

  const categories: EventCategories[] = categoriesData?.data || [];

  const handleCategorySelect = (id: string) => {
    setSelectedCategory(selectedCategory === id ? null : id);
    setShowCustomInput(false);
    setCustomEventName('');
  };

  const handleCustomEventClick = () => {
    setShowCustomInput(true);
    setSelectedCategory(null);
  };

  const handleCustomEventSubmit = () => {
    if (customEventName.trim()) {
      onNext(customEventName.trim());
    }
  };

  const handleContinue = () => {
    if (showCustomInput && customEventName.trim()) {
      handleCustomEventSubmit();
    } else if (selectedCategory) {
      onNext(selectedCategory);
    }
  };

  const textVariants = {
    hidden: {
      opacity: 0.3,
      x: 2,
      scale: 0.99,
    },
    visible: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: 'easeOut',
        staggerChildren: 0.04,
      },
    },
  };

  const letterVariants = {
    hidden: {
      opacity: 0.2,
      x: 2,
      skewX: 2,
    },
    visible: {
      opacity: 1,
      x: 0,
      skewX: 0,
      transition: {
        duration: 0.25,
        ease: [0.2, 0.65, 0.3, 0.9],
      },
    },
  };

  const cardVariants = {
    hidden: {
      x: '-20vw',
      rotate: -4,
      opacity: 0,
    },
    visible: {
      x: 0,
      y: 0,
      rotate: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 180,
        damping: 25,
        mass: 0.2,
        delay: 0.2,
      },
    },
  };

  return (
    <div>
      <motion.h2
        className="text-xl md:text-[40px] font-medium leading-[114.99999999999999%] mb-10"
        initial="hidden"
        animate="visible"
        variants={textVariants}>
        {['What', 'kind', 'of', 'event', 'are'].map((word, i) => (
          <motion.span
            key={i}
            variants={letterVariants}
            style={{
              display: 'inline-block',
              marginRight: '8px',
              transformOrigin: 'left center',
            }}>
            {word}
          </motion.span>
        ))}
        <br />
        {['you', 'planning?'].map((word, i) => (
          <motion.span
            key={i}
            variants={letterVariants}
            style={{
              display: 'inline-block',
              marginRight: '8px',
              transformOrigin: 'left center',
            }}>
            {word}
          </motion.span>
        ))}
      </motion.h2>
      <motion.div
        className="bg-white py-6 px-5 rounded-[20px]"
        initial="hidden"
        animate="visible"
        variants={cardVariants}>
        <div>
          {isLoading ? (
            <div className="flex justify-center py-4">
              <div className="animate-pulse">Loading categories...</div>
            </div>
          ) : categories.length === 0 ? (
            <div className="text-center text-grey-650">
              No categories available
            </div>
          ) : (
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  className={`flex items-center justify-center px-4 py-2 cursor-pointer rounded-full border border-grey-900 ${
                    selectedCategory === category.id
                      ? 'bg-primary-250 border-primary-300 italic font-bold text-dark-blue-200'
                      : 'text-grey font-medium'
                  }`}
                  onClick={() => handleCategorySelect(category.id)}>
                  <span
                    className={`text-sm text-grey font-medium ${
                      selectedCategory !== null &&
                      selectedCategory !== category.id
                        ? 'blur-xs'
                        : 'blur-none'
                    }`}>
                    {category.name}
                  </span>
                </button>
              ))}

              {/* Custom Event Button */}
              <button
                className={`flex items-center gap-1 justify-center px-4 py-2 cursor-pointer rounded-full border border-grey-900 ${
                  showCustomInput
                    ? 'bg-primary-250 border-primary-300 italic font-bold text-dark-blue-200'
                    : 'text-grey bg-[#FAFAFA] font-medium'
                }`}
                onClick={handleCustomEventClick}>
                <AddCircle
                  size={20}
                  variant="Bulk"
                  color={showCustomInput ? '#000059' : '#4D4D4D'}
                />
                <span
                  className={`text-sm font-medium ${
                    showCustomInput ? 'text-dark-blue-200' : 'text-grey'
                  }`}>
                  Custom Event
                </span>
              </button>
            </div>
          )}

          {/* Custom Event Input */}
          {showCustomInput && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="mt-8">
              <h3 className="text-sm  font-medium text-[#414651] mb-4">
                Custom Event
              </h3>
              <input
                type="text"
                placeholder="Type name of Custom Event"
                value={customEventName}
                onChange={(e) => setCustomEventName(e.target.value)}
                className="w-full px-6 h-[44px] border border-[#D5D7DA] rounded-full focus:outline-none  text-black placeholder-[#717680] text-base"
                autoFocus
              />
            </motion.div>
          )}

          <Button
            variant="primary"
            size="md"
            className={`text-white mt-19 ${
              selectedCategory || (showCustomInput && customEventName.trim())
                ? 'bg-primary-650'
                : '!bg-primary-650/35'
            }`}
            onClick={handleContinue}
            iconRight={
              <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
            }>
            Continue
          </Button>
        </div>
      </motion.div>
    </div>
  );
};
