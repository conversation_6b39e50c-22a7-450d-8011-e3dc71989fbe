import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { PageTitle } from "../../../../components/helmet/helmet";
import { useEffect } from "react";
import {
  ArrowLeft,
  Box,
  TickCircle,
  //  Gift,
} from "iconsax-react";
import stackMoney from "../../../../assets/images/stack-money2.svg";
import { Footer } from "../../footer";
// import { Button } from '../../../../components/button/onboardingButton';
import { GiftRegistryServices } from "../../../../lib/services/gift-registry";
import { useQuery } from "@tanstack/react-query";

interface Contributor {
  id: number;
  gifter_first_name: string;
  gifter_last_name: string;
  gifter_email: string;
  status: "pending" | "completed";
}

interface CashGift {
  id: string;
  event_id: string;
  amount: string;
  description: string;
  is_crowd_gift: boolean;
  currency: string;
  status: string;
  created_at: string;
  updated_at: string;
  total_contributions: string;
  total_contributors: number;
  total_reservations: number;
  contributors?: Contributor[];
}

export const CashGiftDetails = () => {
  const { cashId } = useParams();
  const navigate = useNavigate();

  const { data, isLoading } = useQuery({
    queryKey: ["singleCashGift", cashId],
    queryFn: () =>
      cashId
        ? GiftRegistryServices.getSingleCashGift(cashId)
        : Promise.resolve(null),
    enabled: !!cashId,
  });

  const { data: contributors, isLoading: contributorsLoading } = useQuery({
    queryKey: ["cashGiftContributors", cashId],
    queryFn: () =>
      cashId
        ? GiftRegistryServices.getCashGiftContributors(cashId)
        : Promise.resolve(null),
    enabled: !!cashId,
  });

  const cashGift: CashGift | null = data?.data || null;
  const contributorsList = contributors?.data?.reservations || [];

  // console.log('data for contributorsList', contributorsList);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  if (isLoading || contributorsLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  if (!cashGift) {
    return (
      <div className="flex justify-center items-center h-screen">
        Cash gift not found
      </div>
    );
  }

  return (
    <div className="bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <div className="max-w-[564px] mx-auto px-4 md:px-0 pb-20">
        <PageTitle
          title="Cash Gift Details"
          description="View cash gift details"
        />
        <div className="pt-8 mb-6">
          <button
            onClick={() => navigate(-1)}
            className="p-2.5 bg-white rounded-full cursor-pointer"
          >
            <div className="bg-[#F5F6FE] w-fit p-0.5">
              <div className="bg-primary p-0.5 rounded-full">
                <ArrowLeft size="16" color="#fff" />
              </div>
            </div>
          </button>
        </div>

        <div className="">
          <div className="flex pl-5 py-5 flex-wrap md:flex-nowrap gap-4 mb-6 shadow-[0px_12px_120px_0px_#5F5F5F0F] bg-white rounded-2xl ">
            <div className="flex-1 flex justify-between flex-col ">
              <div>
                <p className="text-[28px] font-bold text-black mb-2">
                  ₦{parseFloat(cashGift.amount).toLocaleString()}
                </p>
                <p className="text-base text-grey-100 mb-4">
                  {cashGift.description?.length > 60
                    ? cashGift.description?.slice(0, 60) + "..."
                    : cashGift.description}
                </p>
                {cashGift.is_crowd_gift && (
                  <div className="flex items-center gap-1 mb-4">
                    <TickCircle color="#3CC35C" size={18} variant="Bulk" />
                    <span className="text-sm font-medium text-green-600">
                      Crowd gifting enabled
                    </span>
                  </div>
                )}

                {/* Progress Bar and Contributor Count */}
                {cashGift.is_crowd_gift &&
                  (() => {
                    const totalAmount = parseFloat(cashGift.amount);
                    const totalContributions = parseFloat(
                      cashGift.total_contributions || "0"
                    );
                    const progressPercentage =
                      totalAmount > 0
                        ? Math.min(
                            (totalContributions / totalAmount) * 100,
                            100
                          )
                        : 0;

                    return (
                      <div className="mt-4">
                        <div className="w-full h-2 bg-[#FEF5F1] rounded-lg overflow-hidden">
                          <div
                            className="h-full bg-[#4D55F2] rounded-lg transition-all duration-300"
                            style={{ width: `${progressPercentage}%` }}
                          />
                        </div>
                        <p className="mt-2 text-sm text-[#666666] leading-[1.302] tracking-[-0.02em]">
                          ₦
                          {parseFloat(
                            cashGift.total_contributions || "0"
                          ).toLocaleString()}{" "}
                          contributed by {cashGift.total_contributors || 0}{" "}
                          {cashGift.total_contributors === 1
                            ? "person"
                            : "people"}
                        </p>
                      </div>
                    );
                  })()}
              </div>
              <div className="flex items-center gap-2 mt-4">
                {cashGift.total_reservations > 0 && (
                  <div className="flex items-center gap-1 font-bold italic bg-orange-100 text-[#B54708] text-sm px-2.5 py-1.5 rounded-full">
                    <Box size={12} variant="Bulk" color="#C4320A" />
                    <span>Reserved • {cashGift.total_reservations}</span>
                  </div>
                )}
                {cashGift.total_contributors > 0 && (
                  <div className="flex items-center gap-1 font-bold italic bg-green-100 text-green-700 text-sm px-2.5 py-1.5 rounded-full">
                    <Box color="#3CC35C" size={12} variant="Bulk" />
                    <span>Received • {cashGift.total_contributors}</span>
                  </div>
                )}
              </div>
            </div>
            <div className="flex flex-wrap gap-4 mb-6">
              <img
                src={stackMoney}
                alt="Money Stack"
                className="w-[189px] h-[160px] object-contain"
              />
            </div>
          </div>

          {/* Amount Received */}
          <div className="mb-6 bg-white px-4 py-4 rounded-[16px]">
            <div className="text-[32px] font-bold text-black mb-1 italic">
              ₦
              {parseFloat(
                cashGift?.total_contributions || "0"
              ).toLocaleString()}
            </div>
            <div className="text-xs font-medium text-grey-250 uppercase tracking-[0.10em]">
              AMOUNT RECEIVED
            </div>
          </div>

          {/* Contributors Section */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Contributors</h4>
            <div className="space-y-3">
              {contributorsList && contributorsList.length > 0 ? (
                contributorsList.map((contributor: Contributor) => (
                  <div
                    key={contributor.id}
                    className="flex items-center justify-between p-3 bg-white rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-[#F5F6FE] rounded-full flex items-center justify-center">
                        <span className="text-primary text-base font-semibold">
                          {contributor.gifter_first_name ||
                          contributor.gifter_last_name
                            ? `${
                                contributor.gifter_first_name?.charAt(0) || ""
                              }${
                                contributor.gifter_last_name?.charAt(0) || ""
                              }`.toUpperCase()
                            : "U"}{" "}
                        </span>
                      </div>
                      <div>
                        <div className="text-[#00008C] font-semibold text-sm">
                          {contributor.gifter_first_name &&
                          contributor.gifter_last_name
                            ? `${contributor.gifter_first_name} ${contributor.gifter_last_name}`
                            : "-"}{" "}
                        </div>
                        <div className="text-[12px] text-[#535862]">
                          {contributor.gifter_email || "-"}
                        </div>
                      </div>
                    </div>
                    <div
                      className={`text-sm font-medium italic ${
                        contributor?.status === "pending"
                          ? "text-orange-600"
                          : "text-green-600"
                      }`}
                    >
                      {contributor.status || ""}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500 bg-white rounded-2xl">
                  No contributors yet
                </div>
              )}
            </div>
          </div>

          {/* Reserve Cash Gift Button */}
          {/* <div className="mt-8 pt-6 border-t border-grey-150">
            <Button
              variant="primary"
              size="md"
              className="text-white bg-primary-650 w-full"
              iconLeft={<Gift size="20" color="#fff" variant="Bulk" />}
              onClick={() => navigate(`/giftItem-as-guest`)}>
              Reserve Cash Gift
            </Button>
          </div> */}
        </div>
      </div>
      <Footer />
    </div>
  );
};
