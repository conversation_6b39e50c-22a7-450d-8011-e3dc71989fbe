import { ImageIcon, MapPinIcon, MessageSquareIcon } from "lucide-react";
import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Card, CardContent } from "../../components/ui/card";
import { Tabs, TabsList, TabsTrigger } from "../../components/ui/tabs";
import img from "../../assets/images/attendace.png";
import { ArrowRight, ArrowRight2 } from "iconsax-react";
import icon from "../../assets/images/close-circle.png";

import { EventDetails } from "../../components/EventDetails";
import { EventGallery } from "../../components/EventGallery";
import {
  GalleryConsentModal,
  RejectInvitationModal,
} from "../../components/GalleryConsentModal";

import { GuestPortalSuccess } from "../../components/GuestPortalSuccess";
import { GuestPortalDeclined } from "../../components/GuestPortalDeclined";
import {
  GuestPortalAPI,
  ValidateTokenResponse,
  ApiError,
} from "../../lib/services/guest-portal";
import { toast } from "react-toastify";

export const GuestPortal = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  // Extract token from URL parameters
  const token = searchParams.get("token");

  const [activeTab, setActiveTab] = useState("rsvp");
  const [invitationStatus, setInvitationStatus] = useState<
    "pending" | "accepted" | "rejected"
  >("pending");
  const [userData, setUserData] = useState({
    fullname: "",
    email: "",
    mobile: "",
  });
  const [eventData, setEventData] = useState<ValidateTokenResponse | null>(
    null
  );
  const [showSuccessOverlay, setShowSuccessOverlay] = useState(false);
  const [showDeclinedOverlay, setShowDeclinedOverlay] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [galleryStep, setGalleryStep] = useState<
    "overlay" | "consent" | "usercard" | "declined" | "gallery"
  >("overlay");
  const [showRSVPConsentModal, setShowRSVPConsentModal] = useState(false);
  const [pendingRSVPAction, setPendingRSVPAction] = useState<
    "accept" | "reject" | null
  >(null);
  const [showRejectConfirmModal, setShowRejectConfirmModal] = useState(false);

  // Load invitation data on component mount
  useEffect(() => {
    const loadInvitationData = async () => {
      if (!token) {
        setError("No invitation token provided");
        setIsLoading(false);
        return;
      }

      try {
        setError(null);
        const response = await GuestPortalAPI.validateToken(token);
        setEventData(response);

        // Determine invitation type based on available guest details
        const hasFullDetails =
          response?.guest?.first_name &&
          response?.guest?.last_name &&
          response?.guest?.phone_number &&
          response?.guest?.email;

        const hasEmailOnly =
          response?.guest?.email &&
          (!response?.guest?.first_name ||
            !response?.guest?.last_name ||
            !response?.guest?.phone_number);

        if (hasFullDetails) {
          // Type 1: Full details invitation - populate all fields
          setUserData({
            fullname: `${response?.guest?.first_name} ${response?.guest?.last_name}`,
            email: response?.guest?.email,
            mobile: response?.guest?.phone_number,
          });
        } else if (hasEmailOnly) {
          // Type 2: Email-only invitation - only populate email
          setUserData({
            fullname: "",
            email: response?.guest?.email,
            mobile: "",
          });
        } else {
          // Type 3: No details invitation - empty form
          setUserData({
            fullname: "",
            email: "",
            mobile: "",
          });
        }

        // Check if guest has already filled details (for incomplete invitations)
        const savedUserData = response?.guest?.id
          ? localStorage.getItem(`guest-data-${response?.guest?.id}`)
          : null;
        if (savedUserData && !hasFullDetails) {
          setUserData(JSON.parse(savedUserData));
        }

        // Set invitation status based on API response (with optional chaining for open events)
        if (response?.guest?.invite_status) {
          if (response.guest.invite_status === "accepted") {
            setInvitationStatus("accepted");
          } else if (response.guest.invite_status === "declined") {
            setInvitationStatus("rejected");
          }
          // For other statuses or undefined, keep default "pending"
        }
      } catch (error) {
        console.error("Failed to load invitation data:", error);
        const apiError = error as ApiError;
        setError(apiError.message || "Failed to load invitation");
      } finally {
        setIsLoading(false);
      }
    };

    loadInvitationData();
  }, [token]);

  // Helper function to check if we have complete guest information
  const hasCompleteGuestInfo = () => {
    if (!eventData) return false;

    // Check if we have complete info from form data or guest data
    const [firstName, ...lastNameParts] = userData.fullname.trim().split(" ");
    const lastName = lastNameParts.join(" ");

    const email = userData?.email || eventData?.guest?.email;
    const first_name = firstName || eventData?.guest?.first_name;
    const last_name = lastName || eventData?.guest?.last_name;
    // const phone_number = userData?.mobile || eventData?.guest?.phone_number;

    return !!(email && first_name && last_name);
  };

  const handleAcceptInvitation = async () => {
    if (!eventData) return;

    // Check if we have complete information
    if (!hasCompleteGuestInfo()) {
      setError(
        "Please fill in all required information before accepting the invitation."
      );
      return;
    }

    try {
      const [firstName, ...lastNameParts] = userData.fullname.trim().split(" ");
      const lastName = lastNameParts.join(" ");

      await GuestPortalAPI.rsvp(eventData.event_id, {
        email: userData?.email || eventData?.guest?.email,
        first_name: firstName || eventData?.guest?.first_name,
        last_name: lastName || eventData?.guest?.last_name,
        phone_number: userData?.mobile || eventData?.guest?.phone_number || "",
        status: "accepted",
      });

      setInvitationStatus("accepted");
      localStorage.setItem(
        `invitation-status-${eventData?.guest?.id}`,
        "accepted"
      );
      setShowSuccessOverlay(true);
    } catch (error) {
      console.error("Failed to accept invitation:", error);
      const apiError = error as ApiError;
      setError(apiError.message || "Failed to accept invitation");
    }
  };

  const handleRejectInvitation = () => {
    if (!eventData) return;

    // Check if we have complete information
    if (!hasCompleteGuestInfo()) {
      toast.error(
        "Please fill in all required information before declining the invitation."
      );
      return;
    }

    // Show confirmation modal instead of directly rejecting
    setShowRejectConfirmModal(true);
  };

  const confirmRejectInvitation = async () => {
    if (!eventData) return;

    try {
      const [firstName, ...lastNameParts] = userData.fullname.trim().split(" ");
      const lastName = lastNameParts.join(" ");

      await GuestPortalAPI.rsvp(eventData.event_id, {
        email: userData.email || eventData.guest?.email,
        first_name: firstName || eventData.guest?.first_name,
        last_name: lastName || eventData.guest?.last_name,
        phone_number: userData?.mobile || eventData.guest?.phone_number,
        status: "declined",
      });

      setInvitationStatus("rejected");
      localStorage.setItem(
        `invitation-status-${eventData?.guest?.id}`,
        "rejected"
      );
      setShowDeclinedOverlay(true);
      setShowRejectConfirmModal(false);
    } catch (error) {
      console.error("Failed to reject invitation:", error);
      const apiError = error as ApiError;
      setError(apiError.message || "Failed to reject invitation");
      setShowRejectConfirmModal(false);
    }
  };

  const handleDeclineDirectly = () => {
    // For direct decline (from overlay buttons), check if we have complete info
    if (!hasCompleteGuestInfo()) {
      // If we don't have complete info, navigate to RSVP tab to fill the form
      setActiveTab("rsvp");
      setError("Please fill in your details before declining the invitation.");
      return;
    }

    handleRejectInvitation();
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="bg-white flex flex-row justify-center w-full">
        <div className="bg-white [background:linear-gradient(173deg,rgba(254,247,244,1)_0%,rgba(245,246,254,1)_100%)] w-full min-h-screen relative flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#4d55f2] mx-auto mb-4"></div>
            <p className="text-[#4d55f2] text-lg">Loading invitation...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if no invitation data or error occurred
  if (error || (!isLoading && !eventData)) {
    return (
      <div className=" flex flex-row justify-center w-full">
        <div className="bg-white [background:linear-gradient(173deg,rgba(254,247,244,1)_0%,rgba(245,246,254,1)_100%)] w-full min-h-screen relative flex items-center justify-center">
          <div className="text-center">
            <p className="text-[#990000] text-lg mb-4">
              {error || "Invitation not found"}
            </p>
            <p className="text-[#666666] text-sm">
              This invitation link may be invalid or event has ended . Reach out
              to the host for more information.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const renderTabContent = () => {
    if (activeTab === "rsvp") {
      // Show success/declined states first
      if (invitationStatus === "accepted") {
        return (
          <div className=" flex items-center justify-start pb-12 px-4">
            <div className="relative w-full max-w-[450px]">
              <div className="relative  bg-white border-t border-white rounded-[20px] text-center w-full mx-auto shadow-[0px_12px_120px_0px_#5F5F5F0F]">
                {/* Triangle background */}
                <div
                  className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] relative rounded-t-[20px] flex justify-center items-center h-[262px] w-full overflow-hidden"
                  style={{
                    clipPath:
                      "polygon(0 0, 100% 0, 100% 100%, 70% 95%, 30% 95%, 0 100%)",
                  }}
                >
                  {" "}
                  {/* Image container */}
                  <div className="absolute z-0 top-12 sm:top-[-8px]  overflow-hidden">
                    <img
                      src={eventData?.event_iv_preview_url || img}
                      alt="Wedding celebration"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>

                <div className="flex flex-col items-center text-center py-12 px-4 w-full">
                  <div className="max-w-[322px] w-full">
                    <h2 className="text-base md:text-2xl md:text-4xl font-medium my-2 text-dark-200 md:text-nowrap">
                      Your Attendance has <br />{" "}
                      <span className="text-base md:text-[32px] text-grey-250">
                        been confirmed ︎
                      </span>
                    </h2>
                    <p className="text-grey-250 text-sm md:text-base mt-4 mb-7.5">
                      You're all set! Get ready for an amazing event. Grab your
                      invitation card by clicking the button below
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      if (eventData?.event_iv_preview_url) {
                        window.open(eventData.event_iv_preview_url, "_blank");
                      } else {
                        toast.error(
                          "Invitation card not available for download."
                        );
                      }
                    }}
                    className="bg-primary cursor-pointer text-base  text-white flex items-center py-3 px-6 font-semibold rounded-full gap-2 hover:bg-[#4A48E0] transition-colors"
                  >
                    <span>Download your IV Card</span>
                    <div className="rounded-full bg-white/30 p-0.5">
                      <ArrowRight size="16" color="#fff" />
                    </div>
                  </button>
                </div>
                {/* <div className="flex justify-center mt-5 pb-6">
                  <button
                    type="button"
                    onClick={onClose}
                    className="italic text-[#FF6630] text-base font-medium underline hover:no-underline transition-all"
                  >
                    Continue to Event Details
                  </button>
                </div> */}
              </div>
            </div>
          </div>
        );
      } else if (invitationStatus === "rejected") {
        return (
          <div className="relative w-full max-w-[450px]">
            <div className="relative  bg-white border-t border-white rounded-[20px] text-center w-full mx-auto shadow-[0px_12px_120px_0px_#5F5F5F0F]">
              <div
                className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] relative rounded-t-[20px] h-[262px] w-full overflow-hidden"
                style={{
                  clipPath:
                    "polygon(0 0, 100% 0, 100% 100%, 70% 95%, 30% 95%, 0 100%)",
                }}
              >
                {" "}
                <div className="flex justify-center items-center  h-full">
                  <img src={icon} alt="cancel" />
                </div>
              </div>

              <div className="flex flex-col items-center text-center py-12 px-4 w-full">
                <h2 className="text-base md:text-4xl font-medium my-2 text-dark-blue-100 md:text-nowrap">
                  You Declined <br />{" "}
                  <span className="text-base md:text-[32px] text-grey-250">
                    this invitation
                  </span>
                </h2>
                <p className="text-grey-250 text-sm md:text-base mt-4 mb-7.5">
                  To start creating your own event click the button below
                </p>

                <button
                  type="button"
                  className="bg-primary cursor-pointer text-base  text-white flex items-center py-3 px-6 font-semibold rounded-full gap-2 hover:bg-[#4A48E0] transition-colors"
                >
                  <span>Create Your Own Event</span>
                  <div className="rounded-full bg-white p-0.5">
                    <ArrowRight2 size="12" color="#4D55F2" />
                  </div>{" "}
                </button>
              </div>
            </div>
            {/* <div className="flex justify-center mt-10">
              <button
                type="button"
                onClick={onClose}
                className="text-white text-base font-medium underline hover:no-underline transition-all"
              >
                Back to Event Details
              </button>
            </div> */}
          </div>
        );
      } else if (invitationStatus === "pending") {
        // Determine which form to show based on guest data
        const hasFullDetails =
          eventData?.guest?.first_name &&
          eventData?.guest?.last_name &&
          eventData?.guest?.phone_number &&
          eventData?.guest?.email;

        if (hasFullDetails) {
          // Type 1: Full details - show preview
          return (
            <>
              <Card className="w-full  max-w-[424px] bg-white rounded-xl sm:rounded-2xl overflow-hidden shadow-EVENTPARK-SHADOWS-SM">
                <CardContent className="p-4 sm:p-5">
                  <h2 className="font-semibold text-black text-xl sm:text-2xl tracking-[-0.72px] leading-5 sm:leading-6 mb-4 sm:mb-6">
                    Preview your Details
                  </h2>

                  <div className="flex flex-col w-full items-start gap-1.5 py-2 rounded-xl border-[0.8px] border-solid border-[#e6e6e6] [background:linear-gradient(180deg,rgba(250,250,250,1)_0%,rgba(255,255,255,1)_100%)]">
                    {/* User details rows */}
                    {Object.entries(userData).map(
                      ([key, value], index, array) => (
                        <div
                          key={key}
                          className={`flex items-center justify-between px-3 sm:px-3.5 py-3 sm:py-5 relative self-stretch w-full ${
                            index !== array.length - 1
                              ? "border-b-[0.5px] border-[#f0f0f0]"
                              : ""
                          }`}
                        >
                          <div className="relative w-fit mt-[-0.50px] font-normal text-[#808080] text-sm sm:text-base tracking-[0] leading-[16px] sm:leading-[18px] whitespace-nowrap capitalize">
                            {key}
                          </div>
                          <div
                            className={`relative ${
                              key === "email"
                                ? "w-fit italic"
                                : "w-[140px] sm:w-[194px]"
                            } mt-[-0.50px] font-bold text-black text-sm sm:text-base text-right tracking-[0] leading-[16px] sm:leading-[18px] ${
                              key === "email" ? "whitespace-nowrap" : ""
                            } break-words`}
                          >
                            {value}
                          </div>
                        </div>
                      )
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col w-full items-start gap-3 sm:gap-4 mt-6 sm:mt-8">
                    <Button
                      onClick={() => {
                        setPendingRSVPAction("accept");
                        setShowRSVPConsentModal(true);
                      }}
                      className="relative self-stretch w-full h-[48px] sm:h-[56px] px-6 sm:px-7 bg-[#4d55f2] rounded-[48px] sm:rounded-[64px] text-white text-base sm:text-lg leading-6 sm:leading-7 font-semibold hover:bg-[#3d45d2] transition-colors"
                    >
                      Accept Invitation
                    </Button>
                    <Button
                      onClick={handleRejectInvitation}
                      className="relative self-stretch w-full h-[48px] sm:h-[56px] px-6 sm:px-7 bg-[#fff5f5] rounded-[48px] sm:rounded-[64px] text-[#990000] text-base sm:text-lg leading-6 sm:leading-7 font-semibold hover:bg-[#ffe5e5] transition-colors"
                    >
                      Reject Invitation
                    </Button>
                  </div>
                </CardContent>
              </Card>
              <GalleryConsentModal
                open={showRSVPConsentModal}
                onClose={() => setShowRSVPConsentModal(false)}
                onAgree={() => {
                  setShowRSVPConsentModal(false);
                  if (pendingRSVPAction === "accept") handleAcceptInvitation();
                  setPendingRSVPAction(null);
                }}
                onDisagree={() => {
                  setShowRSVPConsentModal(false);
                  setPendingRSVPAction(null);
                }}
              />
            </>
          );
        } else {
          // Type 2 & 3: Email-only or No details - show form
          return (
            <>
              <Card className="w-full max-w-[424px] bg-white rounded-xl sm:rounded-2xl overflow-hidden shadow-EVENTPARK-SHADOWS-SM">
                <CardContent className="p-4 sm:p-5">
                  <h2 className="font-semibold text-black text-xl sm:text-2xl tracking-[-0.72px] leading-5 sm:leading-6 mb-4 sm:mb-6">
                    Fill in your details
                  </h2>

                  {/* Email display for email-only invitations */}
                  {eventData?.guest?.email && (
                    <div className="mb-4 sm:mb-6 px-2 py-1 bg-[#f5f5f5] rounded-[50px] w-fit border border-[#e6e6e6]">
                      <div className="text-xs sm:text-sm text-[#414651] font-medium">
                        {eventData.guest?.email}
                      </div>
                    </div>
                  )}

                  {/* Form fields */}
                  <div className="space-y-3 sm:space-y-4">
                    {/* First name and Last name - side by side for no-details type */}
                    {!eventData?.guest?.email ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        <div className="space-y-2">
                          <label className="text-xs sm:text-sm font-medium text-[#808080]">
                            First name
                          </label>
                          <input
                            type="text"
                            value={userData.fullname.split(" ")[0] || ""}
                            onChange={(e) => {
                              const lastName = userData.fullname
                                .split(" ")
                                .slice(1)
                                .join(" ");
                              setUserData((prev) => ({
                                ...prev,
                                fullname:
                                  `${e.target.value} ${lastName}`.trim(),
                              }));
                            }}
                            className="w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-[40px] sm:rounded-[50px] border border-[#e6e6e6] bg-white text-black text-sm sm:text-base placeholder:text-[#999999] focus:border-[#4d55f2] focus:outline-none transition-colors"
                            placeholder="Enter first name"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs sm:text-sm font-medium text-[#808080]">
                            Last name
                          </label>
                          <input
                            type="text"
                            value={
                              userData.fullname.split(" ").slice(1).join(" ") ||
                              ""
                            }
                            onChange={(e) => {
                              const firstName =
                                userData.fullname.split(" ")[0] || "";
                              setUserData((prev) => ({
                                ...prev,
                                fullname:
                                  `${firstName} ${e.target.value}`.trim(),
                              }));
                            }}
                            className="w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-[40px] sm:rounded-[50px] border border-[#e6e6e6] bg-white text-black text-sm sm:text-base placeholder:text-[#999999] focus:border-[#4d55f2] focus:outline-none transition-colors"
                            placeholder="Enter Last name"
                          />
                        </div>
                      </div>
                    ) : (
                      <>
                        <div className="space-y-2">
                          <label className="text-xs sm:text-sm font-medium text-[#808080]">
                            First name
                          </label>
                          <input
                            type="text"
                            value={userData.fullname.split(" ")[0] || ""}
                            onChange={(e) => {
                              const lastName = userData.fullname
                                .split(" ")
                                .slice(1)
                                .join(" ");
                              setUserData((prev) => ({
                                ...prev,
                                fullname:
                                  `${e.target.value} ${lastName}`.trim(),
                              }));
                            }}
                            className="w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-[40px] sm:rounded-[50px] border border-[#e6e6e6] bg-white text-black text-sm sm:text-base placeholder:text-[#999999] focus:border-[#4d55f2] focus:outline-none transition-colors"
                            placeholder="Enter your First name"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs sm:text-sm font-medium text-[#808080]">
                            Last name
                          </label>
                          <input
                            type="text"
                            value={
                              userData.fullname.split(" ").slice(1).join(" ") ||
                              ""
                            }
                            onChange={(e) => {
                              const firstName =
                                userData.fullname.split(" ")[0] || "";
                              setUserData((prev) => ({
                                ...prev,
                                fullname:
                                  `${firstName} ${e.target.value}`.trim(),
                              }));
                            }}
                            className="w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-[40px] sm:rounded-[50px] border border-[#e6e6e6] bg-white text-black text-sm sm:text-base placeholder:text-[#999999] focus:border-[#4d55f2] focus:outline-none transition-colors"
                            placeholder="Enter your Last name"
                          />
                        </div>
                      </>
                    )}

                    {/* Email field for no-details type */}
                    {!eventData?.guest?.email && (
                      <div className="space-y-2">
                        <label className="text-xs sm:text-sm font-medium text-[#808080]">
                          Email Address
                        </label>
                        <input
                          type="email"
                          value={userData.email}
                          onChange={(e) =>
                            setUserData((prev) => ({
                              ...prev,
                              email: e.target.value,
                            }))
                          }
                          className="w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-[40px] sm:rounded-[50px] border border-[#e6e6e6] bg-white text-black text-sm sm:text-base placeholder:text-[#999999] focus:border-[#4d55f2] focus:outline-none transition-colors"
                          placeholder="Enter your email address"
                        />
                      </div>
                    )}

                    <div className="space-y-2">
                      <label className="text-xs sm:text-sm font-medium text-[#808080]">
                        Mobile Number
                      </label>
                      <div className="flex rounded-[40px] sm:rounded-[50px] border border-[#e6e6e6] bg-white overflow-hidden focus-within:border-[#4d55f2] transition-colors">
                        <div className="flex items-center px-3 sm:px-4 py-2.5 sm:py-3 bg-white border-r border-[#e6e6e6]">
                          <span className="text-xs sm:text-sm text-[#666666] font-medium">
                            +234
                          </span>
                          <svg
                            className="w-3 h-3 sm:w-4 sm:h-4 ml-1 sm:ml-2 text-[#666666]"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </div>
                        <input
                          type="number"
                          inputMode="numeric"
                          pattern="[0-9]*"
                          onInput={(e: React.FormEvent<HTMLInputElement>) => {
                            const input = e.target as HTMLInputElement;
                            if (input.value.length > 10) {
                              input.value = input.value.slice(0, 10);
                            }
                          }}
                          maxLength={10}
                          value={userData.mobile.replace("+234", "").trim()}
                          onChange={(e) =>
                            setUserData((prev) => ({
                              ...prev,
                              mobile: `+234 ${e.target.value}`,
                            }))
                          }
                          className="flex-1 no-spinner px-3 sm:px-4 py-2.5 sm:py-3 bg-white text-black text-sm sm:text-base placeholder:text-[#999999] focus:outline-none"
                          placeholder="Enter Mobile Number"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col w-full items-start gap-3 sm:gap-4 mt-6 sm:mt-8">
                    <Button
                      onClick={() => {
                        setPendingRSVPAction("accept");
                        setShowRSVPConsentModal(true);
                      }}
                      disabled={
                        !userData.fullname.trim() ||
                        (!eventData?.guest?.email && !userData.email.trim())
                      }
                      className="relative self-stretch w-full h-[48px] sm:h-[56px] px-6 sm:px-7 bg-[#4d55f2] rounded-[48px] sm:rounded-[64px] text-white text-base sm:text-lg leading-6 sm:leading-7 font-semibold hover:bg-[#3d45d2] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Accept Invitation
                    </Button>
                    <Button
                      onClick={handleRejectInvitation}
                      className="relative self-stretch w-full h-[48px] sm:h-[56px] px-6 sm:px-7 bg-[#fff5f5] rounded-[48px] sm:rounded-[64px] text-[#990000] text-base sm:text-lg leading-6 sm:leading-7 font-semibold hover:bg-[#ffe5e5] transition-colors"
                    >
                      Reject Invitation
                    </Button>
                  </div>
                </CardContent>
              </Card>
              <GalleryConsentModal
                open={showRSVPConsentModal}
                onClose={() => setShowRSVPConsentModal(false)}
                onAgree={() => {
                  setShowRSVPConsentModal(false);
                  if (pendingRSVPAction === "accept") handleAcceptInvitation();
                  setPendingRSVPAction(null);
                }}
                onDisagree={() => {
                  setShowRSVPConsentModal(false);
                  setPendingRSVPAction(null);
                }}
              />
            </>
          );
        }
      }
    } else if (activeTab === "details") {
      return (
        <>
          <EventDetails
            invitationStatus={invitationStatus}
            onAcceptToView={() => {
              // Navigate to RSVP tab when user clicks "Accept to view"
              setActiveTab("rsvp");
            }}
            onDecline={handleDeclineDirectly}
            eventData={
              eventData
                ? {
                    event_date_from: eventData.event_date_from,
                    event_date_to: eventData.event_date_to,
                    event_location_address: eventData.event_location_address,
                    event_location_lat: eventData.event_location_lat,
                    event_location_long: eventData.event_location_long,
                    event_location_name: eventData.event_location_name,
                    event_qr_code: eventData.event_qr_code,
                  }
                : undefined
            }
          />
          <GalleryConsentModal
            open={showRSVPConsentModal}
            onClose={() => setShowRSVPConsentModal(false)}
            onAgree={() => {
              setShowRSVPConsentModal(false);
              if (pendingRSVPAction === "accept") handleAcceptInvitation();
              if (pendingRSVPAction === "reject") handleRejectInvitation();
              setPendingRSVPAction(null);
            }}
            onDisagree={() => {
              setShowRSVPConsentModal(false);
              setPendingRSVPAction(null);
            }}
          />
        </>
      );
    } else if (activeTab === "gallery") {
      // Gallery flow wiring
      if (galleryStep === "overlay") {
        return (
          <div>
            <EventGallery
              onAccept={() => {
                // Navigate to RSVP tab when user clicks "Accept to view"
                setActiveTab("rsvp");
              }}
              eventImages={eventData?.event_images}
              invitationStatus={invitationStatus}
            />
          </div>
        );
      }
      if (galleryStep === "consent") {
        return (
          <>
            <EventGallery
              onAccept={() => {
                // Navigate to RSVP tab when user clicks "Accept to view"
                setActiveTab("rsvp");
              }}
              eventImages={eventData?.event_images}
              invitationStatus={invitationStatus}
            />
            <GalleryConsentModal
              open={true}
              onClose={() => setGalleryStep("overlay")}
              onAgree={() => {
                setGalleryStep("overlay");
                setShowSuccessOverlay(true);
              }}
              onDisagree={() => {
                setGalleryStep("overlay");
                setShowDeclinedOverlay(true);
              }}
            />
          </>
        );
      }
      // Default: show gallery grid
      return (
        <EventGallery
          onAccept={() => {
            // Navigate to RSVP tab when user clicks "Accept to view"
            setActiveTab("rsvp");
          }}
          eventImages={eventData?.event_images}
          invitationStatus={invitationStatus}
        />
      );
    }
  };

  return (
    <div className="min-h-screen w-full bg-white [background:linear-gradient(173deg,rgba(254,247,244,1)_0%,rgba(245,246,254,1)_100%)]">
      {/* Header */}
      <header className="w-full h-20 border-b border-[#f5f6fe]">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex items-center h-20">
            <div
              onClick={() => {
                window.location.href = `${window.location.origin}/login`;
              }}
              className="flex items-center cursor-pointer gap-1"
            >
              <img className="w-6 h-6" alt="Vector" src="/vector.svg" />
              <div className="font-bold text-xl text-center tracking-[-0.40px] leading-5 whitespace-nowrap">
                <span className="text-[#000073] tracking-[-0.08px]">
                  EventPark Africa
                </span>
                <span className="text-[#ff6630] tracking-[-0.08px]">.</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 md:px-6 lg:px-8 pt-8">
        {/* Greeting */}
        <div className="font-semibold text-[#ff885e] text-sm sm:text-base  tracking-[1.92px] leading-[17.6px] mb-4 sm:mb-6">
          HI{" "}
          {eventData?.guest?.first_name && eventData?.guest?.last_name
            ? `${eventData.guest.first_name} ${eventData.guest.last_name}`.toUpperCase()
            : "GUEST"}
          ,
        </div>

        {/* Cards Container */}
        <div className="flex flex-col md:flex-row gap-4 sm:gap-6 pb-10">
          {/* Left Column */}
          <div className="flex flex-col gap-4 sm:gap-6 lg:w-1/2">
            {/* Invitation Card */}
            <div className="w-full xl:w-[45%] lg:min-w-[473px] h-[160px] sm:h-[200px] relative overflow-hidden bg-white rounded-2xl shadow-xs">
              <div className="w-full h-full relative">
                {/* <img
                  className="w-full sm:w-[400px] h-full sm:h-[200px] absolute top-0 right-0 object-cover sm:object-contain"
                  alt="Vector"
                  src="/vector-4.svg"
                />
                <img
                  className="w-full sm:w-[400px] h-full sm:h-[200px] absolute top-0 right-0 object-cover sm:object-contain"
                  alt="Vector"
                  src="/vector-3.svg"
                />
                <img
                  className="absolute w-[80px] sm:w-[103px] h-0.5 top-[120px] sm:top-[152px] left-0"
                  alt="Vector"
                  src="/vector-1.svg"
                />
                <img
                  className="absolute w-[40px] sm:w-[52px] h-0.5 top-[80px] sm:top-[105px] left-[30px] sm:left-[50px]"
                  alt="Vector"
                  src="/vector-1.svg"
                /> */}
                <div className="absolute top-4 sm:top-7 left-4 sm:left-[106px] font-semibold text-[#343cd8] text-lg sm:text-[26px] tracking-[-1.04px] leading-[20px] sm:leading-[28.6px] whitespace-nowrap">
                  You&apos;ve been invited 🥳
                </div>
                <img
                  className="absolute w-[250px] sm:w-[336px] h-px top-[50px] sm:top-[76px] left-4 sm:left-[106px] object-cover"
                  alt="Line"
                  src="/line-12.svg"
                />
                <div className="inline-flex items-center gap-2 sm:gap-3.5 absolute top-[65px] sm:top-[101px] left-4 sm:left-[114px]">
                  <div className="relative w-[50px] sm:w-[77.42px] h-[48px] sm:h-[72px]">
                    <div className="relative w-[52px] sm:w-[81px] h-[50px] sm:h-[75px]">
                      <div
                        style={{
                          backgroundImage: eventData?.event_banner_preview_url
                            ? `url(${eventData.event_banner_preview_url})`
                            : "url(/frame-1321314397.svg)",

                          backgroundSize: "cover",
                          backgroundPosition: "center",
                        }}
                        className="absolute w-[40px] sm:w-[62px] h-[40px] sm:h-[62px] top-[3px] sm:top-[5px] left-[3px] sm:left-[5px] bg-[#edeefe] rounded-[8px] sm:rounded-[12.25px] rotate-[-9.53deg]"
                      />
                      <div className="absolute w-[44px] sm:w-[69px] h-[44px] sm:h-[69px] top-[4px] sm:top-[7px] left-2 sm:left-3 rounded-[8px] sm:rounded-[12.25px] border-[2px] sm:border-[3.5px] border-solid border-[#ffffff]  bg-cover bg-[50%_50%]" />
                    </div>
                  </div>
                  <div className="inline-flex flex-col items-start gap-1 sm:gap-1.5 relative flex-[0_0_auto]">
                    <div className="relative self-stretch mt-[-1.00px] font-semibold italic text-black text-sm sm:text-lg tracking-[-0.54px] leading-[16px] sm:leading-[18px]">
                      {eventData?.event_title}
                    </div>
                    <div className="relative self-stretch h-[30px] sm:h-[39px] font-normal text-[#666666] text-xs sm:text-sm tracking-[-0.42px] leading-[14px] sm:leading-[18.9px] max-w-[150px] sm:max-w-[210px] overflow-hidden">
                      {eventData?.event_description}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Tabs Navigation */}
            <div className="w-full">
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
              >
                <TabsList className="flex gap-1 sm:gap-3  bg-white rounded-2xl sm:rounded-3xl p-1 sm:p-1.5 w-full md:w-[383px] h-[44px] sm:h-[50px]">
                  <TabsTrigger
                    value="rsvp"
                    className="inline-flex items-center justify-center gap-1 sm:gap-2 px-2 sm:px-3.5 py-1.5 sm:py-2 bg-transparent rounded-[32px] sm:rounded-[64px] overflow-hidden data-[state=active]:border data-[state=active]:border-solid data-[state=active]:shadow-shadow-xs data-[state=active]:bg-[#4d55f2] data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-[#343cd8] flex-1 sm:flex-none"
                  >
                    <MessageSquareIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                    <span className="text-xs sm:text-sm leading-4 sm:leading-5 font-semibold tracking-[0] whitespace-nowrap">
                      RSVP
                    </span>
                  </TabsTrigger>

                  <TabsTrigger
                    value="details"
                    className="inline-flex items-center justify-center gap-1 sm:gap-2 px-2 sm:px-3.5 py-1.5 sm:py-2 rounded-[32px] sm:rounded-[64px] overflow-hidden data-[state=active]:bg-[#4d55f2] data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-[#343cd8] flex-1 sm:flex-none"
                  >
                    <MapPinIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                    <span className="font-semibold text-xs leading-4 sm:leading-5 tracking-[0] whitespace-nowrap">
                      Details
                    </span>
                  </TabsTrigger>

                  <TabsTrigger
                    value="gallery"
                    className="inline-flex items-center justify-center gap-1 sm:gap-2 px-2 sm:px-3.5 py-1.5 sm:py-2 rounded-[32px] sm:rounded-[64px] overflow-hidden data-[state=active]:bg-[#4d55f2] data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-[#343cd8] flex-1 sm:flex-none"
                  >
                    <ImageIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                    <span className="font-semibold text-xs leading-4 sm:leading-5 tracking-[0] whitespace-nowrap">
                      Gallery
                    </span>
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {/* Tab Content Container */}
            <div className="w-full">{renderTabContent()}</div>
          </div>

          {/* Right Column - Gift Registry */}
          <div
            className={`lg:w-1/2 ${
              activeTab === "gallery" ? "h-1/2" : ""
            } flex flex-col gap-6 sm:gap-[98px]`}
          >
            <Card className="w-full 2xl:ml-[-18%] xl:ml-[-2%] xl:w-[90%] xl:min-w-[512px] h-[160px] sm:h-[200px] rounded-2xl sm:rounded-3xl overflow-hidden shadow-EVENTPARK-SHADOWS-SM [background:linear-gradient(0deg,rgba(77,85,242,1)_0%,rgba(77,85,242,1)_100%)] pt-[0px] px-3 sm:px-5 pb-3 sm:pb-5">
              <CardContent className="relative h-full p-3 sm:p-6">
                <div className="absolute top-[16px] sm:top-[23px] left-1 font-medium text-[#ffffffcc] text-xs sm:text-sm tracking-[1.40px] leading-[normal]">
                  GIFT REGISTRY
                </div>

                <div className="absolute w-full h-full top-0 left-1 overflow-hidden">
                  <img
                    className="w-[180px] sm:w-[267px] h-full sm:h-[200px] right-0 sm:left-[225px] blur-[2px] object-cover absolute top-0"
                    alt="Rectangle"
                    src="/rectangle-72.png"
                  />

                  <div className="absolute w-[200px] sm:w-[244px] h-[40px] sm:h-[58px] top-[32px] sm:top-[47px] left-0 font-normal text-lg sm:text-2xl tracking-[-0.84px] leading-[20px] sm:leading-[28.8px]">
                    {eventData?.event_gift_count &&
                    eventData.event_gift_count > 0 ? (
                      <>
                        <span className="font-medium text-[#cbc6ff] tracking-[-0.20px]">
                          Click to view Items in{" "}
                        </span>
                        <span className="font-medium italic text-white tracking-[-0.20px]">
                          {eventData?.host_first_name &&
                          eventData?.host_last_name
                            ? `${eventData.host_first_name} ${eventData.host_last_name}'s`
                            : "Host's"}{" "}
                          {eventData?.event_title}
                        </span>
                      </>
                    ) : (
                      <span className="font-medium text-[#cbc6ff] tracking-[-0.20px]">
                        {eventData?.host_first_name && eventData?.host_last_name
                          ? `${eventData.host_first_name} ${eventData.host_last_name} hasn't created gift registry yet`
                          : "Host hasn't created gift registry yet"}
                      </span>
                    )}
                  </div>
                </div>

                {eventData?.event_gift_count &&
                eventData.event_gift_count > 0 ? (
                  <div className="w-[90px] sm:w-[116px] absolute bottom-[16px] sm:top-[146px] cursor-pointer left-0 flex items-start rounded-lg">
                    <img
                      onClick={() => {
                        if (eventData?.event_id) {
                          // Store host information in sessionStorage for the gift registry page
                          if (
                            eventData.host_first_name &&
                            eventData.host_last_name
                          ) {
                            sessionStorage.setItem(
                              "guestHostFirstName",
                              eventData.host_first_name
                            );
                            sessionStorage.setItem(
                              "guestHostLastName",
                              eventData.host_last_name
                            );
                          }
                          sessionStorage.setItem(
                            "guestEventId",
                            eventData.event_id
                          );

                          navigate(`/guest/events/${eventData.event_id}/gifts`);
                        } else {
                          console.error("Event ID not found:", eventData);
                          alert(
                            "Event ID not found. Please refresh the page and try again."
                          );
                        }
                      }}
                      className="relative flex-1 grow mt-[-1.00px] cursor-pointer mb-[-3.00px] ml-[-2.00px] mr-[-2.00px] w-full h-auto"
                      alt="EventPark BUTTON"
                      src="/-vendorperk---button-base.svg"
                    />
                  </div>
                ) : null}
              </CardContent>
            </Card>

            {activeTab === "details" && (
              <div className="hidden flex-col gap-4 w-full  max-w-[512px]">
                {/* Map section */}
                <div className="relative rounded-xl border border-[#F0F0F0] overflow-hidden shadow bg-white">
                  <img
                    src="/images/event-map.png"
                    alt="map"
                    className="w-full h-[280px] object-cover"
                  />
                  <div className="absolute top-3 left-4 bg-white  rounded px-3 py-1 flex items-center gap-2">
                    <span className="font-medium text-sm text-[#4D4D4D]">
                      Eko Hotels and Suites, Lagos Nigeria
                    </span>
                  </div>
                  <button
                    className="absolute top-3 right-4 bg-white rounded p-1 shadow"
                    title="Maximize"
                  >
                    <img
                      src="/icons/maximize.svg"
                      alt="maximize"
                      className="w-4 h-4"
                    />
                  </button>
                </div>
                {/* Open address on Google Map */}
                <div className="flex flex-col md:flex-row md:items-center p-2 px-4 shadow-sm w-fit  bg-white rounded-[50px] gap-2 md:gap-4">
                  <span className="font-bold text-sm italic text-[#000059]">
                    Open address on Google Map
                  </span>
                  <a
                    href="https://maps.google.com/?q=Eko+Hotels+and+Suites,+Lagos+Nigeria"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 bg-[#F5F9FF] rounded-[16px] italic h-[28px] px-4 py-1 text-[#5F66F3] font-semibold text-sm hover:bg-[#e8f0fe] transition"
                  >
                    Open link
                    <img
                      src="/icons/link.svg"
                      alt="link"
                      className="w-4 h-4 opacity-60"
                    />
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Success Overlay */}
      {showSuccessOverlay && (
        <GuestPortalSuccess
          banner={eventData?.event_iv_preview_url}
          onClose={() => {
            setShowSuccessOverlay(false);
            setActiveTab("details");
          }}
        />
      )}

      {/* Declined Overlay */}
      {showDeclinedOverlay && (
        <GuestPortalDeclined
          onClose={() => {
            setShowDeclinedOverlay(false);
            setActiveTab("details");
          }}
        />
      )}

      {/* Reject Invitation Confirmation Modal */}
      <RejectInvitationModal
        open={showRejectConfirmModal}
        onClose={() => setShowRejectConfirmModal(false)}
        onConfirm={confirmRejectInvitation}
        onCancel={() => setShowRejectConfirmModal(false)}
      />
    </div>
  );
};
