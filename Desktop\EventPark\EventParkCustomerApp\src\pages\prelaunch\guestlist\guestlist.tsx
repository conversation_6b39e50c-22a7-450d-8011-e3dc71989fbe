import { Calendar, Clock, Profile2User } from "iconsax-react";
import image from "../../../assets/images/image.png";
import { Icon } from "../../../components/icons/icon";
import { GuestTab } from "./guest-tab";
import { useEffect, useState } from "react";
import { useEventStore } from "../../../lib/store/event";
import {
  useInfiniteGuests,
  useGuestSearch,
} from "../../../lib/hooks/useGuestListManagement";
import { formatDate, formattingTime } from "../../../lib/helpers";
import { useUserAuthStore } from "../../../lib/store/auth";
import { PageTitle } from "../../../components/helmet/helmet";
import { GuestCards } from "../guestCards";
import { Modal } from "../../../components/reuseables/prelaunch";
import CreateGuestList from "../create-guest-list/create-guest";
import { useCompleteEventData } from "../../../lib/hooks/useCompleteEventData";

export const GuestList = () => {
  const { selectedEvent } = useEventStore();
  const [isGuestModalOpen, setIsGuestModalOpen] = useState(false);
  const [isSecondGuestModalOpen, setIsSecondGuestModalOpen] = useState(false);
  const [isGuestListModalOpen, setIsGuestListModalOpen] = useState(false);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const {
    guests,
    totalGuests,
    isLoading,
    isLoadingMore,
    hasMorePages,
    isError,
    loadMore,
    meta,
  } = useInfiniteGuests(selectedEvent?.id ?? "", 10);

  const { searchResults, isSearching, isSearchLoading, performSearch } =
    useGuestSearch(selectedEvent?.id ?? "");
  const { toolStatus } = useUserAuthStore();
  const {
    completeEventData,
    isLoading: isLoadingCompleteData,
    // error: completeDataError,
  } = useCompleteEventData(selectedEvent?.id);
  const eventDate = formatDate(selectedEvent?.date_from);
  const eventTime = formattingTime(selectedEvent?.date_from);

  const confirmedCount =
    guests?.filter(
      (guest) => guest?.invite_status?.toLowerCase() === "accepted"
    ).length || 0;
  const pendingCount =
    guests?.filter((guest) => guest?.invite_status?.toLowerCase() === "pending")
      .length || 0;
  const declinedCount =
    guests?.filter(
      (guest) => guest?.invite_status?.toLowerCase() === "declined"
    ).length || 0;
  const closeAllGuestModals = () => {
    setIsGuestModalOpen(false);
    setIsSecondGuestModalOpen(false);
  };
  const openSecondGuestModal = () => {
    setIsGuestModalOpen(false);
    setIsSecondGuestModalOpen(true);
  };
  const navigateToGuestListSetup = () => {
    closeAllGuestModals();
    setIsGuestListModalOpen(true);
  };
  const shareableLink = completeEventData?.invite_link || "";

  const copyToClipboard = () => {
    navigator.clipboard
      .writeText(shareableLink)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      })
      .catch((err) => {
        console.error("Failed to copy: ", err);
      });
  };
  const guestStats = [
    {
      label: "TOTAL GUESTS",
      count: meta?.total || totalGuests || 0,
      icon: <Icon name="profile" />,
    },
    {
      label: "CONFIRMED",
      count: confirmedCount,
      icon: <Icon name="confirmed" />,
    },
    {
      label: "PENDING",
      count: pendingCount,
      icon: <Icon name="pending" />,
    },
    {
      label: "DECLINED",
      count: declinedCount,
      icon: <Icon name="declined" />,
    },
  ];

  if (isLoading || isLoadingCompleteData) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }
  if (isError) {
    return (
      <div className="flex justify-center items-center h-screen">
        <p>Sorry, An error occured. Kindly, Refresh</p>{" "}
      </div>
    );
  }
  return (
    <div className="max-w-[560px] w-full mx-auto pt-32 font-rethink px-4 md:px-0">
      <PageTitle title="Guest List" description="View guest list details" />
      {toolStatus?.has_guestlist &&
      completeEventData?.guestlist_status !== "draft" ? (
        <>
          {/* <div className="flex justify-between mb-6 bg-white pt-6 pl-5 rounded-2xl">
            <div className="max-w-md">
              <h1 className="text-[28px] font-semibold mb-3">
                {selectedEvent?.title || ''}
              </h1>
              <p className="text-sm text-grey-950 mb-7">
                {selectedEvent?.description || ''}
              </p>

              <div className=" flex gap-4 mb-5">
                <div className="flex items-center gap-1 bg-cus-pink-500 pl-2.5 pr-2 py-0.5 rounded-2xl">
                  <Calendar color="#FF885E" size={12} variant="Bulk" />{' '}
                  <span className="text-xs italic font-medium text-cus-orange-250">
                    {eventDate}
                  </span>
                </div>
                <div className="flex items-center gap-1 bg-primary-700 pl-2.5 pr-2 rounded-2xl py-0.5">
                  <Clock color="#000073" size={12} variant="Bulk" />{' '}
                  <span className="text-dark-blue text-xs italic font-medium">
                    {eventTime}
                  </span>
                </div>
              </div>
            </div>

            <img
              src={selectedEvent?.iv_preview_url || image}
              alt="invite-card"
              className="hidden md:block rounded-br-2xl"
            />
          </div> */}
          <div className="flex justify-between mb-6 bg-white pt-6 pl-5 rounded-2xl overflow-hidden md:min-h-[174px]">
            <div className="flex-1 max-w-md pr-4">
              <h1 className="text-[28px] font-semibold mb-2">
                {selectedEvent?.title || ""}
              </h1>
              <p className="text-sm text-grey-950 mb-3 truncate max-w-[300px]">
                {selectedEvent?.description || ""}
              </p>

              <div className="flex gap-4 mb-3">
                <div className="flex items-center gap-1 bg-cus-pink-500 pl-2.5 pr-2 py-0.5 rounded-2xl">
                  <Calendar color="#FF885E" size={12} variant="Bulk" />
                  <span className="text-xs italic font-medium text-cus-orange-250">
                    {eventDate}
                  </span>
                </div>
                <div className="flex items-center gap-1 bg-primary-700 pl-2.5 pr-2 rounded-2xl py-0.5">
                  <Clock color="#000073" size={12} variant="Bulk" />
                  <span className="text-dark-blue text-xs italic font-medium">
                    {eventTime}
                  </span>
                </div>
              </div>
              {selectedEvent?.visibility !== "private" && (
                <div className="flex mb-2 items-center justify-between bg-[#F5F9FF] max-w-[300px] rounded-full p-1 pl-4 italic">
                  <span className="text-sm text-[#000059]  truncate mr-2">
                    <strong>Invite Link:</strong> {shareableLink}
                  </span>
                  <button
                    type="button"
                    onClick={copyToClipboard}
                    className="bg-[#E1ECFE] text-primary flex items-center gap-1 py-1.5 px-4 rounded-full"
                  >
                    <span className="text-sm font-medium">
                      {copied ? "Copied!" : "Copy"}
                    </span>
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 12 12"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8 6.45V8.55C8 10.3 7.3 11 5.55 11H3.45C1.7 11 1 10.3 1 8.55V6.45C1 4.7 1.7 4 3.45 4H5.55C7.3 4 8 4.7 8 6.45Z"
                        fill="#4D55F2"
                      />
                      <path
                        opacity="0.4"
                        d="M8.55281 1H6.45281C4.72781 1 4.02781 1.685 4.00781 3.375H5.55281C7.65281 3.375 8.62781 4.35 8.62781 6.45V7.995C10.3178 7.975 11.0028 7.275 11.0028 5.55V3.45C11.0028 1.7 10.3028 1 8.55281 1Z"
                        fill="#4D55F2"
                      />
                    </svg>
                  </button>
                </div>
              )}
            </div>

            <div className="hidden md:flex flex-shrink-0 w-56 h-full relative items-end ">
              <img
                src={selectedEvent?.iv_preview_url || image}
                alt="invite-card"
                className="w-full h-full object-cover  rounded-t-2xl rotate-[2deg]"
              />
            </div>
          </div>
          <div className="bg-white rounded-2xl mb-6">
            <div className="grid grid-cols-1 md:grid-cols-4 divide-y md:divide-x divide-grey-850">
              {guestStats.map((stat, index) => (
                <div key={index} className=" pr-3 pl-4 pt-3 pb-5">
                  <div className="flex justify-end mb-3">{stat.icon}</div>
                  <div className="text-[32px] italic font-bold mb-1.5">
                    {stat.count}
                  </div>
                  <div className="text-grey-250 text-xs font-medium uppercase tracking-[0.10em]">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
          <GuestTab
            guests={guests}
            onLoadMore={loadMore}
            isLoadingMore={isLoadingMore}
            hasMorePages={hasMorePages}
            onSearch={performSearch}
            searchResults={searchResults}
            isSearching={isSearching}
            isSearchLoading={isSearchLoading}
          />
        </>
      ) : (
        <div>
          <div className="">
            <h1 className="text-2xl font-semibold">Guestlists</h1>
            <p className="text-grey-950 text-base">
              Welcome back, how can we help today?
            </p>
          </div>
          <div className="flex flex-col items-center justify-center mb-40 mt-25">
            <Profile2User size={200} variant="Bulk" color="#B8BBFA" />
            <h2 className="text-xl font-medium mb-3 mt-10">No Guests Yet</h2>
            <p className="text-gray-500 text-center mb-10">
              You have not added any guests to your event yet, <br />
              Get started by clicking the button below{" "}
            </p>

            <button
              type="button"
              onClick={() =>
                toolStatus?.has_guestlist
                  ? setIsGuestListModalOpen(true)
                  : setIsGuestModalOpen(true)
              }
              className="bg-primary text-white font-semibold py-3 px-6 rounded-full hover:bg-primary/90"
            >
              Create Guest List
            </button>
          </div>
        </div>
      )}
      <Modal
        isOpen={isGuestModalOpen}
        onClose={closeAllGuestModals}
        actionButton={openSecondGuestModal}
        forwardActionText="Continue"
        secondModalAnimation={true}
        title="Guestlist "
        name="Manager"
        subtitle="Effortless Invites, Perfect Attendance"
        description={
          <p>
            Send invites, track RSVPs, and manage <br /> your guest list
            seamlessly—all in one place.
            <br /> No stress, just perfect planning!
          </p>
        }
        leftContent={<GuestCards />}
        leftContentMobile={true}
      />
      <Modal
        isOpen={isSecondGuestModalOpen}
        onClose={closeAllGuestModals}
        actionButton={navigateToGuestListSetup}
        forwardActionText="Continue"
        secondModalAnimation={true}
        title="Guestlist "
        name="Manager"
        subtitle="WHAT TO EXPECT"
        description={
          <div className="mt-2">
            <p className="mb-5 text-base text-grey-550 font-normal">
              Here are some steps to follow to set up your guest manager
            </p>

            <div className="space-y-4 font-semibold italic">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-650 text-white flex items-center justify-center text-sm font-medium">
                  1
                </div>
                <p className="text-sm text-dark-blue-400 text-left ">
                  Select Invite from pre designed IV templates or alternately
                  upload your own Invite{" "}
                </p>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-650 text-white flex items-center justify-center text-sm font-medium">
                  2
                </div>
                <p className="text-sm text-dark-blue-400 text-left ">
                  Add Guests to your Guestlist{" "}
                </p>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-650 text-white flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <p className="text-sm text-dark-blue-400 text-left">
                  Preview and Send out your Invites{" "}
                </p>
              </div>
            </div>
          </div>
        }
        leftContent={<GuestCards />}
        leftContentMobile={true}
      />
      {isGuestListModalOpen && (
        <CreateGuestList onClose={() => setIsGuestListModalOpen(false)} />
      )}
    </div>
  );
};

export default GuestList;
